<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.sounfury</groupId>
        <artifactId>sounfury_blog</artifactId>
        <version>${revision}</version>
    </parent>


    <artifactId>common</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>common-bom</module>
        <module>common-core</module>
        <module>common-jooq</module>
        <module>common-web</module>
        <module>common-satoken</module>
        <module>common-redis</module>
        <module>common-oss</module>
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>



    <description>
        common 通用模块
    </description>






</project>