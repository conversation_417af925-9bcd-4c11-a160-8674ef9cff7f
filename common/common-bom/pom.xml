<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.sounfury</groupId>
    <artifactId>common-bom</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>




    <properties>
        <revision>0.0.1</revision>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.sounfury</groupId>
                <artifactId>common-jooq</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.sounfury</groupId>
                <artifactId>common-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.sounfury</groupId>
                <artifactId>common-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.sounfury</groupId>
                <artifactId>common-satoken</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.sounfury</groupId>
                <artifactId>common-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.sounfury</groupId>
                <artifactId>common-oss</artifactId>
                <version>${revision}</version>
            </dependency>


        </dependencies>
    </dependencyManagement>

</project>