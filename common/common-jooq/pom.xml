<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.sounfury</groupId>
        <artifactId>common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>common-jooq</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>


        <dependency>
            <groupId>org.sounfury</groupId>
            <artifactId>common-core</artifactId>
            <version>0.0.1</version>
        </dependency>
        <!-- jooq -->
        <dependency>
            <groupId>org.jooq</groupId>
            <artifactId>jooq</artifactId>
            <version>${jooq.version}</version>
        </dependency>

        <dependency>
            <groupId>org.jooq</groupId>
            <artifactId>jooq-codegen-maven</artifactId>
            <scope>runtime</scope>
            <version>${jooq.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>


    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.jooq</groupId>
                <artifactId>jooq-codegen-maven</artifactId>
                <version>${jooq.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <phase>generate-sources</phase>
                    </execution>
                </executions>
                <configuration>
                    <jdbc>
                        <driver>com.mysql.cj.jdbc.Driver</driver>
                        <url>****************************************************************************************************************************************</url>
                        <user>root</user>
                        <password>a2133266</password>
                    </jdbc>
                    <generator>
                        <database>
                            <name>org.jooq.meta.mysql.MySQLDatabase</name>
                            <inputSchema>blog</inputSchema>
                                <forcedTypes>
                                    <forcedType>
                                        <name>BIGINT</name>
                                        <includeTypes>.*INT\s*UNSIGNED.*</includeTypes>
                                    </forcedType>
                            </forcedTypes>
                        </database>
                        <target>
                            <packageName>org.sounfury.jooq</packageName>
                            <directory>target/generated-sources/jooq</directory>
                        </target>

                        <generate>
                            <daos>true</daos> <!-- 生成 DAO 类 -->
                            <pojos>true</pojos> <!-- 生成 POJO 类 -->
                            <fluentSetters>true</fluentSetters> <!-- 为 POJO 类启用流式 setter -->
                            <springAnnotations>true</springAnnotations> <!-- 为 DAO 添加 Spring 注解 -->
                            <immutablePojos>false</immutablePojos> <!-- 生成可变的 POJO 类 -->
                        </generate>
                    </generator>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>