<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.sounfury</groupId>
        <artifactId>common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>common-oss</artifactId>

    <description>
        common-oss oss服务
    </description>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.sounfury</groupId>
            <artifactId>common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
            <exclusions>
                <!-- 将基于 Netty 的 HTTP 客户端从类路径中移除 -->
                <exclusion>
                    <groupId>software.amazon.awssdk</groupId>
                    <artifactId>netty-nio-client</artifactId>
                </exclusion>
                <!-- 将基于 CRT 的 HTTP 客户端从类路径中移除 -->
                <exclusion>
                    <groupId>software.amazon.awssdk</groupId>
                    <artifactId>aws-crt-client</artifactId>
                </exclusion>
                <!-- 将基于 Apache 的 HTTP 客户端从类路径中移除 -->
                <exclusion>
                    <groupId>software.amazon.awssdk</groupId>
                    <artifactId>apache-client</artifactId>
                </exclusion>
                <!-- 将配置基于 URL 连接的 HTTP 客户端从类路径中移除 -->
                <exclusion>
                    <groupId>software.amazon.awssdk</groupId>
                    <artifactId>url-connection-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 使用AWS基于 CRT 的 S3 客户端 -->
        <dependency>
            <groupId>software.amazon.awssdk.crt</groupId>
            <artifactId>aws-crt</artifactId>
        </dependency>

        <!-- 基于 AWS CRT 的 S3 客户端的性能增强的 S3 传输管理器 -->
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3-transfer-manager</artifactId>
        </dependency>

        <dependency>
            <groupId>org.sounfury</groupId>
            <artifactId>common-redis</artifactId>
        </dependency>
    </dependencies>

</project>