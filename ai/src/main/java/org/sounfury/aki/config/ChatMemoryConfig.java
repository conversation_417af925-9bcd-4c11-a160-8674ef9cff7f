package org.sounfury.aki.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.PromptChatMemoryAdvisor;
import org.springframework.ai.chat.memory.repository.jdbc.JdbcChatMemoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Spring AI ChatMemory配置
 * 配置基于JDBC的聊天记忆存储
 */
@Slf4j
@Configuration
public class ChatMemoryConfig {

    @Autowired
    private JdbcChatMemoryRepository chatMemoryRepository;

    /**
     * 配置ChatMemory
     * 使用Spring AI自动配置的JdbcChatMemoryRepository
     */
    @Bean
    public ChatMemory chatMemory() {
        log.info("初始化MessageWindowChatMemory，使用自动配置的JdbcChatMemoryRepository");
        return MessageWindowChatMemory.builder()
                .chatMemoryRepository(chatMemoryRepository)
                .maxMessages(50) // 最多保留50条历史消息
                .build();
    }

    /**
     * 配置消息记忆Advisor
     * 将历史消息作为Message对象注入到对话中
     */
    @Bean
    public MessageChatMemoryAdvisor messageChatMemoryAdvisor(ChatMemory chatMemory) {
        log.info("初始化MessageChatMemoryAdvisor");
        return MessageChatMemoryAdvisor.builder(chatMemory)
                .build();
    }

    /**
     * 配置提示词记忆Advisor
     * 将历史对话作为文本追加到系统提示词中
     */
    @Bean
    public PromptChatMemoryAdvisor promptChatMemoryAdvisor(ChatMemory chatMemory) {
        log.info("初始化PromptChatMemoryAdvisor");
        return PromptChatMemoryAdvisor.builder(chatMemory)
                .build();
    }
}
