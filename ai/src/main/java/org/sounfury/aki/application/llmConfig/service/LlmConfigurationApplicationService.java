package org.sounfury.aki.application.llmConfig.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.application.llmConfig.dto.LlmConfigurationDto;
import org.sounfury.aki.application.llmConfig.dto.UpdateLlmConfigurationCommand;
import org.sounfury.aki.domain.llm.config.LlmConfiguration;
import org.sounfury.aki.domain.llm.ModelProvider;
import org.sounfury.aki.domain.llm.config.ModelSettings;
import org.sounfury.aki.domain.llm.config.LlmConfigurationRepository;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

/**
 * LLM配置应用服务
 * 处理LLM配置相关的业务操作
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LlmConfigurationApplicationService {
    
    private final LlmConfigurationRepository configurationRepository;
    private final ApplicationEventPublisher eventPublisher;
    
    /**
     * 获取当前全局LLM配置
     */
    public LlmConfigurationDto getCurrentConfiguration() {
        LlmConfiguration configuration = configurationRepository.findGlobalConfiguration()
                .orElseThrow(() -> new RuntimeException("全局LLM配置不存在"));
        
        return LlmConfigurationDto.fromDomain(configuration);
    }
    
    /**
     * 更新LLM配置
     */
    public LlmConfigurationDto updateConfiguration(UpdateLlmConfigurationCommand command) {
        log.info("开始更新LLM配置: {}", command);
        
        try {
            // 1. 获取当前配置
            LlmConfiguration currentConfig = configurationRepository.findGlobalConfiguration()
                    .orElse(LlmConfiguration.createDefault());
            
            // 2. 构建新的提供商配置
            ModelProvider newProvider = buildModelProvider(command);
            
            // 3. 构建新的模型设置
            ModelSettings newSettings = buildModelSettings(command);
            
            // 4. 更新配置（会自动发布事件）
            LlmConfiguration updatedConfig = currentConfig.updateConfiguration(
                    newProvider, newSettings, eventPublisher);
            
            // 5. 保存配置
            LlmConfiguration savedConfig = configurationRepository.save(updatedConfig);
            
            log.info("LLM配置更新成功: Provider={}, Model={}", 
                    savedConfig.getProvider().getDisplayName(),
                    savedConfig.getProvider().getModelName());
            
            return LlmConfigurationDto.fromDomain(savedConfig);
            
        } catch (Exception e) {
            log.error("更新LLM配置失败: {}", e.getMessage(), e);
            throw new RuntimeException("更新LLM配置失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 启用LLM配置
     */
    public LlmConfigurationDto enableConfiguration() {
        log.info("启用LLM配置");
        
        LlmConfiguration currentConfig = configurationRepository.findGlobalConfiguration()
                .orElseThrow(() -> new RuntimeException("全局LLM配置不存在"));
        
        LlmConfiguration enabledConfig = currentConfig.enable(eventPublisher);
        LlmConfiguration savedConfig = configurationRepository.save(enabledConfig);
        
        log.info("LLM配置已启用");
        return LlmConfigurationDto.fromDomain(savedConfig);
    }
    
    /**
     * 禁用LLM配置
     */
    public LlmConfigurationDto disableConfiguration() {
        log.info("禁用LLM配置");
        
        LlmConfiguration currentConfig = configurationRepository.findGlobalConfiguration()
                .orElseThrow(() -> new RuntimeException("全局LLM配置不存在"));
        
        LlmConfiguration disabledConfig = currentConfig.disable(eventPublisher);
        LlmConfiguration savedConfig = configurationRepository.save(disabledConfig);
        
        log.info("LLM配置已禁用");
        return LlmConfigurationDto.fromDomain(savedConfig);
    }
    
    /**
     * 重置为默认配置
     */
    public LlmConfigurationDto resetToDefault() {
        log.info("重置LLM配置为默认值");
        
        LlmConfiguration defaultConfig = LlmConfiguration.createDefault();
        
        // 如果存在当前配置，通过更新方式触发事件
        LlmConfiguration currentConfig = configurationRepository.findGlobalConfiguration().orElse(null);
        if (currentConfig != null) {
            LlmConfiguration updatedConfig = currentConfig.updateConfiguration(
                    defaultConfig.getProvider(), 
                    defaultConfig.getSettings(), 
                    eventPublisher);
            
            LlmConfiguration savedConfig = configurationRepository.save(updatedConfig);
            log.info("LLM配置已重置为默认值");
            return LlmConfigurationDto.fromDomain(savedConfig);
        } else {
            // 直接保存默认配置
            LlmConfiguration savedConfig = configurationRepository.save(defaultConfig);
            log.info("创建默认LLM配置");
            return LlmConfigurationDto.fromDomain(savedConfig);
        }
    }
    
    /**
     * 构建模型提供商
     */
    private ModelProvider buildModelProvider(UpdateLlmConfigurationCommand command) {
        ModelProvider.ProviderType providerType;
        try {
            providerType = ModelProvider.ProviderType.valueOf(command.getProviderType().toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("不支持的提供商类型: " + command.getProviderType());
        }

        return ModelProvider.create(
                providerType,
                command.getBaseUrl(),
                command.getApiKey(),
                command.getModelName(),
                command.getDisplayName()
        );
    }
    
    /**
     * 构建模型设置
     */
    private ModelSettings buildModelSettings(UpdateLlmConfigurationCommand command) {
        ModelSettings.ModelSettingsBuilder builder = ModelSettings.builder();
        
        if (command.getMaxTokens() != null) {
            builder.maxTokens(command.getMaxTokens());
        }
        if (command.getTemperature() != null) {
            builder.temperature(command.getTemperature());
        }
        if (command.getTopP() != null) {
            builder.topP(command.getTopP());
        }
        if (command.getFrequencyPenalty() != null) {
            builder.frequencyPenalty(command.getFrequencyPenalty());
        }
        if (command.getPresencePenalty() != null) {
            builder.presencePenalty(command.getPresencePenalty());
        }
        if (command.getTimeoutSeconds() != null) {
            builder.timeoutSeconds(command.getTimeoutSeconds());
        }
        if (command.getRetryCount() != null) {
            builder.retryCount(command.getRetryCount());
        }
        
        return builder.build();
    }
}
