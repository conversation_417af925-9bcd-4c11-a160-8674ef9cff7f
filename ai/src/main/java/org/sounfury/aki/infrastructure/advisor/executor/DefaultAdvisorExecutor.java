package org.sounfury.aki.infrastructure.advisor.executor;

import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.common.DomainAdvisor;
import org.sounfury.aki.domain.prompt.advisor.BehaviorGuideDomainAdvisor;
import org.sounfury.aki.domain.character.advisor.CharacterCardDomainAdvisor;
import org.sounfury.aki.domain.prompt.advisor.SystemPromptDomainAdvisor;
import org.sounfury.aki.domain.prompt.advisor.TaskSpecificDomainAdvisor;
import org.sounfury.aki.domain.prompt.advisor.UserAddressDomainAdvisor;
import org.springframework.ai.chat.client.ChatClientRequest;
import org.springframework.ai.chat.client.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.CallAdvisorChain;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisorChain;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;

/**
 * 默认Advisor执行器实现
 * 负责执行具体的DomainAdvisor逻辑
 */
@Slf4j
@Component
public class DefaultAdvisorExecutor implements AdvisorExecutor {
    
    @Override
    public ChatClientResponse executeCall(DomainAdvisor domainAdvisor, ChatClientRequest request, CallAdvisorChain chain) {
        log.debug("执行同步Advisor: {}", domainAdvisor.getName());
        
        // 根据Advisor类型执行不同的逻辑
        ChatClientRequest enhancedRequest = enhanceRequest(domainAdvisor, request);
        
        // 继续链式调用
        return chain.nextCall(enhancedRequest);
    }
    
    @Override
    public Flux<ChatClientResponse> executeStream(DomainAdvisor domainAdvisor, ChatClientRequest request, StreamAdvisorChain chain) {
        log.debug("执行流式Advisor: {}", domainAdvisor.getName());
        
        // 根据Advisor类型执行不同的逻辑
        ChatClientRequest enhancedRequest = enhanceRequest(domainAdvisor, request);
        
        // 继续链式调用
        return chain.nextStream(enhancedRequest);
    }
    
    /**
     * 根据DomainAdvisor类型增强请求
     */
    private ChatClientRequest enhanceRequest(DomainAdvisor domainAdvisor, ChatClientRequest request) {
        return switch (domainAdvisor.getType()) {
            case SYSTEM_PROMPT -> enhanceWithSystemPrompt((SystemPromptDomainAdvisor) domainAdvisor, request);
            case CHARACTER_CARD -> enhanceWithCharacterCard((CharacterCardDomainAdvisor) domainAdvisor, request);
            case USER_ADDRESS -> enhanceWithUserAddress((UserAddressDomainAdvisor) domainAdvisor, request);
            case BEHAVIOR_GUIDE -> enhanceWithBehaviorGuide((BehaviorGuideDomainAdvisor) domainAdvisor, request);
            case TASK_SPECIFIC -> enhanceWithTaskSpecific((TaskSpecificDomainAdvisor) domainAdvisor, request);
            default -> {
                log.warn("未支持的Advisor类型: {}", domainAdvisor.getType());
                yield request;
            }
        };
    }
    
    /**
     * 使用系统提示词增强请求
     */
    private ChatClientRequest enhanceWithSystemPrompt(SystemPromptDomainAdvisor advisor, ChatClientRequest request) {
        String systemPrompt = advisor.getSystemPrompt();
        if (systemPrompt == null || systemPrompt.trim().isEmpty()) {
            log.debug("系统提示词为空，跳过增强");
            return request;
        }
        
        return addSystemMessage(request, systemPrompt, "系统提示词");
    }
    
    /**
     * 使用角色卡增强请求
     */
    private ChatClientRequest enhanceWithCharacterCard(CharacterCardDomainAdvisor advisor, ChatClientRequest request) {
        String characterPrompt = advisor.getCharacterPrompt();
        if (characterPrompt == null || characterPrompt.trim().isEmpty()) {
            log.debug("角色卡提示词为空，跳过增强");
            return request;
        }
        
        return addSystemMessage(request, characterPrompt, "角色卡提示词");
    }
    
    /**
     * 使用用户称呼增强请求
     */
    private ChatClientRequest enhanceWithUserAddress(UserAddressDomainAdvisor advisor, ChatClientRequest request) {
        String userAddressPrompt = advisor.getUserAddressPrompt();
        if (userAddressPrompt == null || userAddressPrompt.trim().isEmpty()) {
            log.debug("用户称呼提示词为空，跳过增强");
            return request;
        }

        return addSystemMessage(request, userAddressPrompt, "用户称呼提示词");
    }

    /**
     * 使用行为指导增强请求
     */
    private ChatClientRequest enhanceWithBehaviorGuide(BehaviorGuideDomainAdvisor advisor, ChatClientRequest request) {
        String behaviorPrompt = advisor.getBehaviorGuidePrompt();
        if (behaviorPrompt == null || behaviorPrompt.trim().isEmpty()) {
            log.debug("行为指导提示词为空，跳过增强");
            return request;
        }

        return addSystemMessage(request, behaviorPrompt, "行为指导提示词");
    }

    /**
     * 使用任务特定提示词增强请求
     */
    private ChatClientRequest enhanceWithTaskSpecific(TaskSpecificDomainAdvisor advisor, ChatClientRequest request) {
        String taskPrompt = advisor.getTaskSpecificPrompt();
        if (taskPrompt == null || taskPrompt.trim().isEmpty()) {
            log.debug("任务特定提示词为空，跳过增强");
            return request;
        }

        return addSystemMessage(request, taskPrompt, "任务特定提示词");
    }
    
    /**
     * 向请求中添加系统消息
     */
    private ChatClientRequest addSystemMessage(ChatClientRequest request, String content, String type) {
        // 获取现有的Prompt
        Prompt currentPrompt = request.prompt();
        List<org.springframework.ai.chat.messages.Message> messages = new ArrayList<>(currentPrompt.getInstructions());
        
        // 添加系统消息
        messages.add(new SystemMessage(content));
        log.debug("添加{}: {}", type, content.substring(0, Math.min(50, content.length())));
        
        // 创建新的Prompt
        Prompt enhancedPrompt = new Prompt(messages, currentPrompt.getOptions());
        
        // 返回增强后的请求
        return request.mutate()
                .prompt(enhancedPrompt)
                .build();
    }
}
