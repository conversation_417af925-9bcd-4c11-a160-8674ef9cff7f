package org.sounfury.aki.infrastructure.advisor.factory;

import org.sounfury.aki.domain.common.DomainAdvisor;
import org.sounfury.aki.infrastructure.advisor.SpringAiAdvisorAdapter;
import org.springframework.ai.chat.client.advisor.api.Advisor;

import java.util.List;

/**
 * Advisor转换工具类
 * 负责将DomainAdvisor转换为Spring AI的Advisor
 */
public class AdvisorConvertUtils {

    /**
     * 将DomainAdvisor转换为Spring AI Advisor
     * @param domainAdvisor 领域Advisor
     * @return Spring AI Advisor
     */
    public static Advisor createSpringAiAdvisor(DomainAdvisor domainAdvisor) {
        return new SpringAiAdvisorAdapter(domainAdvisor);
    }

    /**
     * 批量转换DomainAdvisor为Spring AI Advisor
     * @param domainAdvisors 领域Advisor列表
     * @return Spring AI Advisor列表
     */
    public static List<Advisor> createSpringAiAdvisors(List<DomainAdvisor> domainAdvisors) {
        return domainAdvisors.stream()
                .map(AdvisorConvertUtils::createSpringAiAdvisor)
                .toList();
    }
}
