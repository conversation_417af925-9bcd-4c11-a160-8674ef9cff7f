package org.sounfury.aki.infrastructure.advisor.factory;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.common.DomainAdvisor;
import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;
import org.sounfury.aki.domain.service.task.enums.TaskMode;
import org.sounfury.aki.domain.prompt.advisor.PromptDomainAdvisor;
import org.sounfury.aki.domain.prompt.advisor.SystemPromptDomainAdvisor;
import org.sounfury.aki.domain.chatsession.advisor.SessionContextAdvisor;
import org.sounfury.aki.domain.chatsession.ChatSession;
import org.springframework.stereotype.Service;

/**
 * 提示词Advisor创建服务
 * 负责创建各种提示词相关的DomainAdvisor实例
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpringAiPromptAdvisorFactory {

    private final SystemPromptManager systemPromptManager;

    public DomainAdvisor createSystemPromptAdvisor() {
        log.debug("创建系统提示词Advisor");
        return new SystemPromptDomainAdvisor(systemPromptManager);
    }

    public DomainAdvisor createUserAddressAdvisor(String userName) {
        log.debug("创建用户称呼Advisor，用户名: {}", userName);
        return PromptDomainAdvisor.createUserAddressAdvisor(systemPromptManager, userName);
    }

    public DomainAdvisor createBehaviorGuideAdvisor(TemplateType behaviorType) {
        log.debug("创建行为指导Advisor，类型: {}", behaviorType.getName());
        return PromptDomainAdvisor.createBehaviorGuideAdvisor(systemPromptManager, behaviorType);
    }

    public DomainAdvisor createTaskSpecificAdvisor(TaskMode taskMode) {
        log.debug("创建任务特定Advisor，任务模式: {}", taskMode.getCode());
        return PromptDomainAdvisor.createTaskSpecificAdvisor(systemPromptManager, taskMode);
    }

    // ===== Session相关Advisor创建方法 =====

    /**
     * 创建会话上下文Advisor
     * @param session 当前会话
     * @param includeStats 是否包含统计信息
     * @return 会话上下文Advisor
     */
    public DomainAdvisor createSessionContextAdvisor(ChatSession session, boolean includeStats) {
        log.debug("创建会话上下文Advisor，会话ID: {}, 包含统计: {}",
                session.getSessionId().getValue(), includeStats);
        return new SessionContextAdvisor(session, includeStats);
    }

    /**
     * 创建会话上下文Advisor（默认包含统计信息）
     * @param session 当前会话
     * @return 会话上下文Advisor
     */
    public DomainAdvisor createSessionContextAdvisor(ChatSession session) {
        return createSessionContextAdvisor(session, true);
    }
}
