package org.sounfury.aki.infrastructure.advisor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClientRequest;
import org.springframework.ai.chat.client.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.CallAdvisor;
import org.springframework.ai.chat.client.advisor.api.CallAdvisorChain;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisor;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisorChain;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.prompt.Prompt;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;

/**
 * 简单提示词Advisor
 * 直接实现Spring AI的Advisor接口，用于处理提示词增强
 */
@Slf4j
@RequiredArgsConstructor
public class SimplePromptAdvisor implements CallAdvisor, StreamAdvisor {
    
    private final String name;
    private final int order;
    private final String promptContent;
    
    @Override
    public String getName() {
        return name;
    }
    
    @Override
    public int getOrder() {
        return order;
    }
    
    @Override
    public ChatClientResponse adviseCall(ChatClientRequest request, CallAdvisorChain chain) {
        log.debug("SimplePromptAdvisor [{}] 处理同步请求", name);
        
        if (isValidPrompt(promptContent)) {
            ChatClientRequest enhancedRequest = addSystemMessage(request, promptContent);
            return chain.nextCall(enhancedRequest);
        } else {
            log.debug("SimplePromptAdvisor [{}] 提示词为空，跳过处理", name);
            return chain.nextCall(request);
        }
    }
    
    @Override
    public Flux<ChatClientResponse> adviseStream(ChatClientRequest request, StreamAdvisorChain chain) {
        log.debug("SimplePromptAdvisor [{}] 处理流式请求", name);
        
        if (isValidPrompt(promptContent)) {
            ChatClientRequest enhancedRequest = addSystemMessage(request, promptContent);
            return chain.nextStream(enhancedRequest);
        } else {
            log.debug("SimplePromptAdvisor [{}] 提示词为空，跳过处理", name);
            return chain.nextStream(request);
        }
    }
    
    /**
     * 检查提示词是否有效
     */
    private boolean isValidPrompt(String prompt) {
        return prompt != null && !prompt.trim().isEmpty();
    }
    
    /**
     * 向请求中添加系统消息
     */
    private ChatClientRequest addSystemMessage(ChatClientRequest request, String content) {
        try {
            Prompt originalPrompt = request.prompt();
            List<org.springframework.ai.chat.messages.Message> messages = new ArrayList<>(originalPrompt.getInstructions());
            
            // 添加系统消息到消息列表开头
            messages.add(0, new SystemMessage(content));
            
            // 创建新的Prompt
            Prompt newPrompt = new Prompt(messages, originalPrompt.getOptions());
            
            // 返回增强后的请求
            return request.mutate()
                    .prompt(newPrompt)
                    .build();
                    
        } catch (Exception e) {
            log.error("SimplePromptAdvisor [{}] 添加系统消息失败: {}", name, e.getMessage(), e);
            return request; // 降级处理，返回原始请求
        }
    }
}
