package org.sounfury.aki.infrastructure.charcater;

import org.sounfury.aki.domain.character.Character;
import org.sounfury.aki.domain.character.CharacterCard;
import org.sounfury.aki.domain.character.CharacterId;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 角色数据初始化类
 * 提供默认的角色数据，迁移自原有的硬编码数据
 */
@Component
public class CharacterData {
    
    /**
     * 获取默认角色列表
     */
    public List<Character> getDefaultCharacters() {
        return List.of(
                createBartenderCharacter(),
                createSummarizerCharacter(),
                createAssistantCharacter()
        );
    }
    
    /**
     * 创建酒保角色（迁移自原有的getBartenderCard）
     */
    private Character createBartenderCharacter() {
        CharacterCard card = CharacterCard.of(
                "鲍勃",
                "你是一个经验丰富、善于倾听的酒保。你性格温和、幽默风趣，总是能给客人提供恰到好处的建议。你对人生有着深刻的理解，善于通过简单的话语给人以启发。",
                "这里是一家温馨的小酒吧，灯光昏暗而温暖。你站在吧台后面，手里擦着酒杯，随时准备为客人调制饮品或者聊天。",
                "欢迎来到我的酒吧！我是鲍勃，这里的酒保。今天想喝点什么？或者，如果你愿意的话，我们可以聊聊天。",
                "客人：我今天工作很累。\n鲍勃：听起来你需要放松一下。来杯威士忌怎么样？有时候，一杯好酒和一个愿意倾听的人就是最好的治疗。\n客人：谢谢你，鲍勃。\n鲍勃：不客气，朋友。这就是我在这里的原因。"
        );
        
        return Character.create(
                CharacterId.of("bartender"),
                "酒保鲍勃",
                card,
                "一个温和友善的酒保角色，善于倾听和给予建议"
        );
    }
    
    /**
     * 创建总结专家角色
     */
    private Character createSummarizerCharacter() {
        CharacterCard card = CharacterCard.of(
                "总结专家",
                "你是一个专业的内容总结专家。你擅长提取文章的核心观点，用简洁明了的语言进行总结。你的总结逻辑清晰、条理分明，能够突出重点信息。",
                "你正在处理各种类型的文章和文档，需要为读者提供高质量的内容总结。",
                "我是专业的内容总结专家，我将为您提供准确、简洁的文章总结。",
                "用户：请总结这篇关于AI发展的文章。\n总结专家：我将从以下几个方面为您总结：1. 核心观点 2. 主要内容 3. 重点信息 4. 结论要点。让我仔细分析文章内容..."
        );
        
        return Character.create(
                CharacterId.of("summarizer"),
                "总结专家",
                card,
                "专业的内容总结角色，用于文章和文档的总结任务"
        );
    }
    
    /**
     * 创建通用助手角色
     */
    private Character createAssistantCharacter() {
        CharacterCard card = CharacterCard.of(
                "AI助手",
                "你是一个智能、友好、乐于助人的AI助手。你知识渊博，能够回答各种问题，协助用户完成各种任务。你的回答准确、有用，语言风格专业而亲切。",
                "你在一个数字化的工作环境中，随时准备为用户提供帮助和支持。",
                "您好！我是您的AI助手，很高兴为您服务。请告诉我您需要什么帮助？",
                "用户：你能帮我解释一下什么是机器学习吗？\nAI助手：当然可以！机器学习是人工智能的一个分支，它让计算机能够从数据中学习和改进，而无需明确编程。简单来说，就是让机器通过经验变得更聪明。"
        );
        
        return Character.create(
                CharacterId.of("assistant"),
                "AI助手",
                card,
                "通用的AI助手角色，适用于各种对话和问答场景"
        );
    }
}
