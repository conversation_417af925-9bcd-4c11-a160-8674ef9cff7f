package org.sounfury.aki.infrastructure.chatsession;

import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.chatsession.ChatSession;
import org.sounfury.aki.domain.chatsession.SessionId;
import org.sounfury.aki.domain.chatsession.SessionRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 内存版会话仓储实现
 * 用于开发和测试环境
 */
@Slf4j
@Repository
public class InMemorySessionRepository implements SessionRepository {
    
    private final ConcurrentMap<String, ChatSession> sessions = new ConcurrentHashMap<>();
    
    @Override
    public void save(ChatSession session) {
        if (session == null || session.getSessionId() == null) {
            throw new IllegalArgumentException("会话或会话ID不能为空");
        }
        
        sessions.put(session.getSessionId().getValue(), session);
        log.debug("保存会话: {}", session.getSessionId().getValue());
    }
    
    @Override
    public Optional<ChatSession> findById(SessionId sessionId) {
        if (sessionId == null) {
            return Optional.empty();
        }
        
        ChatSession session = sessions.get(sessionId.getValue());
        log.debug("查找会话: {}, 结果: {}", sessionId.getValue(), session != null ? "找到" : "未找到");
        return Optional.ofNullable(session);
    }
    
    @Override
    public boolean exists(SessionId sessionId) {
        if (sessionId == null) {
            return false;
        }
        
        boolean exists = sessions.containsKey(sessionId.getValue());
        log.debug("检查会话是否存在: {}, 结果: {}", sessionId.getValue(), exists);
        return exists;
    }
    
    @Override
    public void deleteById(SessionId sessionId) {
        if (sessionId == null) {
            return;
        }
        
        ChatSession removed = sessions.remove(sessionId.getValue());
        if (removed != null) {
            log.info("删除会话: {}", sessionId.getValue());
        } else {
            log.debug("尝试删除不存在的会话: {}", sessionId.getValue());
        }
    }
    
    @Override
    public long count() {
        return sessions.size();
    }
    
    @Override
    public int cleanupExpiredSessions(int timeoutMinutes) {
        int cleanedCount = 0;
        
        for (var entry : sessions.entrySet()) {
            ChatSession session = entry.getValue();
            if (session.isExpired(timeoutMinutes)) {
                sessions.remove(entry.getKey());
                cleanedCount++;
                log.debug("清理过期会话: {}", entry.getKey());
            }
        }
        
        if (cleanedCount > 0) {
            log.info("清理了 {} 个过期会话", cleanedCount);
        }
        
        return cleanedCount;
    }
}
