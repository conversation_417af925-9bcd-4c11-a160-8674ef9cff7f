package org.sounfury.aki.infrastructure.advisor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.advisor.config.AdvisorConfiguration;
import org.sounfury.aki.domain.advisor.config.RagSettings;
import org.sounfury.aki.domain.prompt.template.service.CharacterPromptManager;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Spring AI Advisor组装器
 * 负责将领域配置转换为Spring AI的Advisor实例
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpringAiAdvisorAssembler {
    
    private final SystemPromptManager systemPromptManager;
    private final CharacterPromptManager characterPromptManager;
    // TODO: 注入世界书向量存储提供者
    // private final WorldBookVectorStoreProvider vectorStoreProvider;
    
    /**
     * 根据配置组装Advisor列表
     * @param config Advisor配置
     * @return Spring AI Advisor列表
     */
    public List<Advisor> assembleAdvisors(AdvisorConfiguration config) {
        log.debug("开始组装Advisor，配置: {}", config);
        
        List<Advisor> advisors = new ArrayList<>();
        
        // 1. 系统提示词Advisor（优先级100，最先执行）
        advisors.add(createSystemPromptAdvisor());
        
        // 2. 角色卡Advisor（优先级200）
        if (config.hasCharacter()) {
            Advisor characterAdvisor = createCharacterAdvisor(config.getCharacterId());
            if (characterAdvisor != null) {
                advisors.add(characterAdvisor);
            }
        }
        
        // 3. 用户称呼Advisor（优先级300）
        if (config.hasUserName()) {
            advisors.add(createUserAddressAdvisor(config.getUserName()));
        }
        
        // 4. 行为指导Advisor（优先级400）
        if (config.hasBehaviorType()) {
            advisors.add(createBehaviorGuideAdvisor(config.getBehaviorType()));
        }
        
        // 5. RAG Advisor（优先级500）
        if (config.isRagFullyConfigured()) {
            Advisor ragAdvisor = createRagAdvisor(config.getRagSettings(), config.getCharacterId());
            if (ragAdvisor != null) {
                advisors.add(ragAdvisor);
            }
        }
        
        log.debug("Advisor组装完成，共创建 {} 个Advisor", advisors.size());
        return advisors;
    }
    
    /**
     * 创建系统提示词Advisor
     */
    private Advisor createSystemPromptAdvisor() {
        log.debug("创建系统提示词Advisor");
        String systemPrompt = systemPromptManager.buildBaseSystemPrompt();
        return new SimplePromptAdvisor("SystemPromptAdvisor", 100, systemPrompt);
    }
    
    /**
     * 创建角色卡Advisor
     */
    private Advisor createCharacterAdvisor(String characterId) {
        log.debug("创建角色卡Advisor，角色ID: {}", characterId);
        
        try {
            String characterPrompt = characterPromptManager.buildCompleteCharacterPrompt(characterId);
            if (characterPrompt == null || characterPrompt.trim().isEmpty()) {
                log.warn("角色ID {} 的角色卡提示词为空", characterId);
                return null;
            }
            
            return new SimplePromptAdvisor("CharacterCardAdvisor", 200, characterPrompt);
            
        } catch (Exception e) {
            log.error("创建角色卡Advisor失败，角色ID: {}", characterId, e);
            return null;
        }
    }
    
    /**
     * 创建用户称呼Advisor
     */
    private Advisor createUserAddressAdvisor(String userName) {
        log.debug("创建用户称呼Advisor，用户名: {}", userName);
        
        try {
            String userAddressPrompt = systemPromptManager.buildPrompt(
                    org.sounfury.aki.domain.prompt.template.TemplateType.USER_ADDRESS,
                    Map.of("user_name", userName)
            );
            
            return new SimplePromptAdvisor("UserAddressAdvisor", 300, userAddressPrompt);
            
        } catch (Exception e) {
            log.error("创建用户称呼Advisor失败，用户名: {}", userName, e);
            return new SimplePromptAdvisor("UserAddressAdvisor", 300, 
                    "用户的名字是" + userName + "，你可以称呼他为" + userName + "。");
        }
    }
    
    /**
     * 创建行为指导Advisor
     */
    private Advisor createBehaviorGuideAdvisor(org.sounfury.aki.domain.prompt.template.TemplateType behaviorType) {
        log.debug("创建行为指导Advisor，类型: {}", behaviorType.getName());
        
        try {
            String behaviorPrompt = systemPromptManager.buildPrompt(behaviorType, Map.of());
            return new SimplePromptAdvisor("BehaviorGuideAdvisor", 400, behaviorPrompt);
            
        } catch (Exception e) {
            log.error("创建行为指导Advisor失败，类型: {}", behaviorType.getName(), e);
            return null;
        }
    }
    
    /**
     * 创建RAG Advisor
     */
    private Advisor createRagAdvisor(RagSettings ragSettings, String characterId) {
        log.debug("创建RAG Advisor，角色ID: {}, 设置: {}", characterId, ragSettings);
        
        // TODO: 实现RAG Advisor创建逻辑
        // 暂时返回null，等世界书功能实现后再完善
        log.warn("RAG功能暂未实现，跳过RAG Advisor创建");
        return null;
        
        /*
        try {
            VectorStore vectorStore = vectorStoreProvider.getVectorStore(characterId);
            if (vectorStore == null) {
                log.warn("角色ID {} 没有对应的向量存储", characterId);
                return null;
            }
            
            SearchRequest.Builder searchRequestBuilder = SearchRequest.builder()
                    .similarityThreshold(ragSettings.getSimilarityThreshold())
                    .topK(ragSettings.getTopK());
            
            // 如果有启用的世界书，添加过滤条件
            if (ragSettings.hasEnabledWorldBooks()) {
                String filterExpression = buildWorldBookFilter(ragSettings.getEnabledWorldBooks());
                searchRequestBuilder.filterExpression(filterExpression);
            }
            
            QuestionAnswerAdvisor.Builder advisorBuilder = QuestionAnswerAdvisor.builder(vectorStore)
                    .searchRequest(searchRequestBuilder.build());
            
            // 如果有自定义提示词模板，使用自定义模板
            if (ragSettings.hasCustomPromptTemplate()) {
                PromptTemplate customTemplate = PromptTemplate.builder()
                        .template(ragSettings.getCustomPromptTemplate())
                        .build();
                advisorBuilder.promptTemplate(customTemplate);
            }
            
            return advisorBuilder.build();
            
        } catch (Exception e) {
            log.error("创建RAG Advisor失败，角色ID: {}", characterId, e);
            return null;
        }
        */
    }
    
    /**
     * 构建世界书过滤表达式
     */
    private String buildWorldBookFilter(List<String> enabledWorldBooks) {
        if (enabledWorldBooks.isEmpty()) {
            return null;
        }
        
        // 构建类似 "worldbook in ['book1', 'book2']" 的过滤表达式
        StringBuilder filter = new StringBuilder("worldbook in [");
        for (int i = 0; i < enabledWorldBooks.size(); i++) {
            if (i > 0) {
                filter.append(", ");
            }
            filter.append("'").append(enabledWorldBooks.get(i)).append("'");
        }
        filter.append("]");
        
        return filter.toString();
    }
}
