package org.sounfury.aki.infrastructure.advisor.factory;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.common.DomainAdvisor;
import org.sounfury.aki.domain.prompt.template.service.CharacterPromptManager;
import org.sounfury.aki.domain.character.advisor.CharacterCardDomainAdvisor;
import org.springframework.stereotype.Service;

/**
 * 角色Advisor创建服务
 * 负责创建角色相关的DomainAdvisor实例
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpringAiCharacterAdvisorFactory {
    
    private final CharacterPromptManager characterPromptManager;
    
    /**
     * 默认角色ID（酒保）
     */
    private static final String DEFAULT_CHARACTER_ID = "bartender";
    
    public DomainAdvisor createCharacterAdvisor(String characterId) {
        log.debug("创建角色卡Advisor，角色ID: {}", characterId);
        return new CharacterCardDomainAdvisor(characterPromptManager, characterId);
    }

    public DomainAdvisor createDefaultCharacterAdvisor() {
        log.debug("创建默认角色Advisor，角色ID: {}", DEFAULT_CHARACTER_ID);
        return new CharacterCardDomainAdvisor(characterPromptManager, DEFAULT_CHARACTER_ID);
    }
}
