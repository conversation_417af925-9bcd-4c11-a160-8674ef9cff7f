package org.sounfury.aki.infrastructure.prompttemplate;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.prompt.template.PromptTemplate;
import org.sounfury.aki.domain.prompt.template.PromptTemplateRepository;
import org.sounfury.aki.domain.prompt.template.TemplateId;
import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 内存版提示词模板仓储实现
 * 用于开发和测试阶段，后续可替换为数据库实现
 */
@Slf4j
@Repository
public class InMemoryPromptTemplateRepository implements PromptTemplateRepository {
    
    private final Map<TemplateId, PromptTemplate> templates = new ConcurrentHashMap<>();
    private final PromptTemplateData templateData;
    
    public InMemoryPromptTemplateRepository(PromptTemplateData templateData) {
        this.templateData = templateData;
    }
    
    @PostConstruct
    public void init() {
        // 初始化默认模板数据
        templateData.getDefaultTemplates().forEach(this::save);
        log.info("初始化提示词模板仓储完成，加载了 {} 个模板", templates.size());
    }
    
    @Override
    public Optional<PromptTemplate> findById(TemplateId id) {
        return Optional.ofNullable(templates.get(id));
    }
    
    @Override
    public List<PromptTemplate> findByTypeAndEnabled(TemplateType type, boolean enabled) {
        return templates.values().stream()
                .filter(template -> template.getType() == type && template.isEnabled() == enabled)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<PromptTemplate> findByType(TemplateType type) {
        return templates.values().stream()
                .filter(template -> template.getType() == type)
                .collect(Collectors.toList());
    }
    
    @Override
    public Optional<PromptTemplate> findByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return Optional.empty();
        }
        
        return templates.values().stream()
                .filter(template -> name.trim().equals(template.getName()))
                .findFirst();
    }
    
    @Override
    public List<PromptTemplate> findAllEnabled() {
        return templates.values().stream()
                .filter(PromptTemplate::isEnabled)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<PromptTemplate> findAll() {
        return List.copyOf(templates.values());
    }
    
    @Override
    public PromptTemplate save(PromptTemplate template) {
        if (template == null) {
            throw new IllegalArgumentException("模板不能为空");
        }
        
        templates.put(template.getId(), template);
        log.debug("保存提示词模板: {}", template.getName());
        return template;
    }
    
    @Override
    public void deleteById(TemplateId id) {
        if (id == null) {
            return;
        }
        
        PromptTemplate removed = templates.remove(id);
        if (removed != null) {
            log.debug("删除提示词模板: {}", removed.getName());
        }
    }
    
    @Override
    public boolean existsById(TemplateId id) {
        return id != null && templates.containsKey(id);
    }
    
    @Override
    public boolean existsByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        
        return templates.values().stream()
                .anyMatch(template -> name.trim().equals(template.getName()));
    }
    
    @Override
    public Optional<PromptTemplate> findByTypeAndName(TemplateType type, String name) {
        if (type == null || name == null || name.trim().isEmpty()) {
            return Optional.empty();
        }
        
        return templates.values().stream()
                .filter(template -> template.getType() == type && name.trim().equals(template.getName()))
                .findFirst();
    }
}
