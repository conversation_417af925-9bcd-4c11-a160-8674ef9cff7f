package org.sounfury.aki.infrastructure.llm;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.llm.ModelCapability;

import org.sounfury.aki.domain.llm.config.LlmConfiguration;
import org.sounfury.aki.domain.llm.config.LlmConfigurationRepository;
import org.sounfury.aki.domain.llm.service.ChatModelManager;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * 默认聊天模型服务实现
 * 基于ConfigurableChatModelFactory管理ChatModel实例
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultChatModelManager implements ChatModelManager {
    
    private final ConfigurableChatModelFactory chatModelFactory;
    private final LlmConfigurationRepository configurationRepository;
    
    @Override
    public ChatModel getCurrentChatModel() {
        try {
            LlmConfiguration configuration = getCurrentConfiguration();
            return chatModelFactory.createOrGetChatModel(configuration);
        } catch (Exception e) {
            log.error("获取当前ChatModel失败，使用默认配置: {}", e.getMessage(), e);
            LlmConfiguration defaultConfig = LlmConfiguration.createDefault();
            return chatModelFactory.createOrGetChatModel(defaultConfig);
        }
    }
    
    @Override
    public void refreshChatModel() {
        try {
            LlmConfiguration configuration = getCurrentConfiguration();
            chatModelFactory.recreateChatModel(configuration);
            log.info("ChatModel刷新成功: Provider={}, Model={}",
                    configuration.getProvider().getDisplayName(),
                    configuration.getProvider().getModelName());
        } catch (Exception e) {
            log.error("刷新ChatModel失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public LlmConfiguration getCurrentConfiguration() {
        return configurationRepository.findGlobalConfiguration()
                .orElseThrow(() -> new RuntimeException("全局LLM配置不存在"));
    }

    // ===== 内部辅助方法（保留以支持其他功能） =====

    /**
     * 检查当前模型是否支持特定能力
     * @param capability 模型能力
     * @return 是否支持该能力
     */
    public boolean hasCapability(ModelCapability capability) {
        try {
            LlmConfiguration configuration = getCurrentConfiguration();
            return switch (capability) {
                case FUNCTION_CALL -> configuration.getProvider().supportsFunctionCall();
                case MULTIMODAL -> configuration.getProvider().supportsMultimodal();
            };
        } catch (Exception e) {
            log.error("检查模型能力失败: {}", e.getMessage());
            return false;
        }
    }
}
