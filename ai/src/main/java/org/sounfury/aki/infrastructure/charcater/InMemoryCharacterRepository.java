package org.sounfury.aki.infrastructure.charcater;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.character.Character;
import org.sounfury.aki.domain.character.CharacterId;
import org.sounfury.aki.domain.character.CharacterRepository;
import org.springframework.stereotype.Repository;


import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 内存版角色仓储实现
 * 用于开发和测试阶段，后续可替换为数据库实现
 */
@Slf4j
@Repository
public class InMemoryCharacterRepository implements CharacterRepository {
    
    private final Map<CharacterId, Character> characters = new ConcurrentHashMap<>();
    private final CharacterData characterData;
    
    public InMemoryCharacterRepository(CharacterData characterData) {
        this.characterData = characterData;
    }
    
    @PostConstruct
    public void init() {
        // 初始化默认角色数据
        characterData.getDefaultCharacters().forEach(this::save);
        log.info("初始化角色仓储完成，加载了 {} 个角色", characters.size());
    }
    
    @Override
    public Optional<Character> findById(CharacterId id) {
        return Optional.ofNullable(characters.get(id));
    }
    
    @Override
    public Optional<Character> findByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return Optional.empty();
        }
        
        return characters.values().stream()
                .filter(character -> name.trim().equals(character.getName()))
                .findFirst();
    }
    
    @Override
    public List<Character> findAllEnabled() {
        return characters.values().stream()
                .filter(Character::isEnabled)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Character> findAll() {
        return List.copyOf(characters.values());
    }
    
    @Override
    public Character save(Character character) {
        if (character == null) {
            throw new IllegalArgumentException("角色不能为空");
        }
        
        characters.put(character.getId(), character);
        log.debug("保存角色: {}", character.getName());
        return character;
    }
    
    @Override
    public void deleteById(CharacterId id) {
        if (id == null) {
            return;
        }
        
        Character removed = characters.remove(id);
        if (removed != null) {
            log.debug("删除角色: {}", removed.getName());
        }
    }
    
    @Override
    public boolean existsById(CharacterId id) {
        return id != null && characters.containsKey(id);
    }
    
    @Override
    public boolean existsByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        
        return characters.values().stream()
                .anyMatch(character -> name.trim().equals(character.getName()));
    }
}
