package org.sounfury.aki.infrastructure.advisor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.common.DomainAdvisor;
import org.springframework.ai.chat.client.ChatClientRequest;
import org.springframework.ai.chat.client.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.CallAdvisor;
import org.springframework.ai.chat.client.advisor.api.CallAdvisorChain;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisor;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisorChain;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.prompt.Prompt;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;

/**
 * Spring AI Advisor适配器
 * 将领域层的DomainAdvisor适配为Spring AI的Advisor接口
 * 直接处理DomainAdvisor逻辑，无需额外的执行器
 */
@Slf4j
@RequiredArgsConstructor
public class SpringAiAdvisorAdapter implements CallAdvisor, StreamAdvisor {

    private final DomainAdvisor domainAdvisor;
    
    @Override
    public String getName() {
        return domainAdvisor.getName();
    }
    
    @Override
    public int getOrder() {
        return domainAdvisor.getOrder();
    }
    
    @Override
    public ChatClientResponse adviseCall(ChatClientRequest request, CallAdvisorChain chain) {
        log.debug("SpringAiAdvisorAdapter 处理请求: {}", domainAdvisor.getName());

        // 直接处理DomainAdvisor逻辑
        ChatClientRequest enhancedRequest = enhanceRequest(domainAdvisor, request);

        // 继续链式调用
        return chain.nextCall(enhancedRequest);
    }

    @Override
    public Flux<ChatClientResponse> adviseStream(ChatClientRequest request, StreamAdvisorChain chain) {
        log.debug("SpringAiAdvisorAdapter 处理流式请求: {}", domainAdvisor.getName());

        // 直接处理DomainAdvisor逻辑
        ChatClientRequest enhancedRequest = enhanceRequest(domainAdvisor, request);

        // 继续链式调用
        return chain.nextStream(enhancedRequest);
    }

    /**
     * 使用DomainAdvisor增强请求
     * 技术适配层：调用领域层的buildPrompt方法，然后适配到Spring AI
     */
    private ChatClientRequest enhanceRequest(DomainAdvisor domainAdvisor, ChatClientRequest request) {
        try {
            // 调用领域层DomainAdvisor构建提示词（技术无关）
            String promptContent = domainAdvisor.buildPrompt();

            if (promptContent == null || promptContent.trim().isEmpty()) {
                log.debug("Advisor {} 返回空提示词，跳过增强", domainAdvisor.getName());
                return request;
            }

            // 技术适配：将领域提示词添加到Spring AI的请求中
            return addSystemMessage(request, promptContent, domainAdvisor.getName());

        } catch (Exception e) {
            log.error("Advisor {} 处理失败: {}", domainAdvisor.getName(), e.getMessage(), e);
            return request; // 降级处理，返回原始请求
        }
    }

    /**
     * 向请求中添加系统消息
     */
    private ChatClientRequest addSystemMessage(ChatClientRequest request, String content, String advisorName) {
        // 获取现有的Prompt
        Prompt currentPrompt = request.prompt();
        List<org.springframework.ai.chat.messages.Message> messages = new ArrayList<>(currentPrompt.getInstructions());

        // 添加系统消息
        messages.add(new SystemMessage(content));
        log.debug("Advisor {} 添加系统消息: {}", advisorName, content.substring(0, Math.min(50, content.length())));

        // 创建新的Prompt
        Prompt enhancedPrompt = new Prompt(messages, currentPrompt.getOptions());

        // 返回增强后的请求
        return request.mutate()
                .prompt(enhancedPrompt)
                .build();
    }
}
