package org.sounfury.aki.infrastructure.advisor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.advisor.config.MemorySettings;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.ChatMemoryRepository;
import org.springframework.ai.chat.memory.InMemoryChatMemoryRepository;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.stereotype.Service;

/**
 * 记忆Advisor工厂
 * 根据存储类型创建不同的记忆Advisor实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MemoryAdvisorFactory {
    
    private final MessageChatMemoryAdvisor jdbcMemoryAdvisor; // 现有的JDBC记忆Advisor
    
    /**
     * 根据存储类型创建记忆Advisor
     * @param memorySettings 记忆设置
     * @return 对应的记忆Advisor
     */
    public Advisor createMemoryAdvisor(MemorySettings memorySettings) {
        if (memorySettings == null) {
            log.warn("记忆设置为空，返回null");
            return null;
        }
        
        log.debug("创建记忆Advisor，存储类型: {}, 最大历史数量: {}", 
                memorySettings.getMemoryStorageType(), 
                memorySettings.getMaxHistorySize());
        
        return switch (memorySettings.getMemoryStorageType()) {
            case SESSION_ONLY -> createSessionMemoryAdvisor(memorySettings);
            case DATABASE -> createDatabaseMemoryAdvisor(memorySettings);
            case HYBRID -> createHybridMemoryAdvisor(memorySettings);
        };
    }
    
    /**
     * 创建会话内存Advisor（游客使用）
     * 使用InMemoryChatMemoryRepository，页面刷新即清除
     */
    private Advisor createSessionMemoryAdvisor(MemorySettings settings) {
        log.debug("创建会话内存Advisor，最大消息数: {}", settings.getMaxHistorySize());
        
        try {
            // 使用Spring AI的InMemoryChatMemoryRepository
            ChatMemoryRepository inMemoryRepository = new InMemoryChatMemoryRepository();
            
            ChatMemory chatMemory = MessageWindowChatMemory.builder()
                    .chatMemoryRepository(inMemoryRepository)
                    .maxMessages(settings.getMaxHistorySize())
                    .build();
            
            Advisor advisor = MessageChatMemoryAdvisor.builder(chatMemory).build();
            log.debug("会话内存Advisor创建成功");
            return advisor;
            
        } catch (Exception e) {
            log.error("创建会话内存Advisor失败: {}", e.getMessage(), e);
            // 降级处理：返回一个最小配置的内存Advisor
            return createFallbackSessionMemoryAdvisor();
        }
    }
    
    /**
     * 创建数据库记忆Advisor（站长使用）
     */
    private Advisor createDatabaseMemoryAdvisor(MemorySettings settings) {
        log.debug("使用数据库记忆Advisor");
        
        // 使用现有的JDBC记忆Advisor
        // TODO: 未来可以根据settings调整JDBC记忆的配置
        return jdbcMemoryAdvisor;
    }
    
    /**
     * 创建混合记忆Advisor（未来扩展）
     */
    private Advisor createHybridMemoryAdvisor(MemorySettings settings) {
        log.debug("混合记忆模式暂未实现，降级为会话内存");
        
        // TODO: 实现混合存储策略
        // 例如：短期记忆用内存，长期记忆用数据库
        return createSessionMemoryAdvisor(settings);
    }
    
    /**
     * 创建降级的会话内存Advisor
     * 当主要创建方法失败时使用
     */
    private Advisor createFallbackSessionMemoryAdvisor() {
        log.warn("使用降级的会话内存Advisor");
        
        try {
            ChatMemoryRepository inMemoryRepository = new InMemoryChatMemoryRepository();
            ChatMemory chatMemory = MessageWindowChatMemory.builder()
                    .chatMemoryRepository(inMemoryRepository)
                    .maxMessages(10) // 默认最大消息数
                    .build();
            
            return MessageChatMemoryAdvisor.builder(chatMemory).build();
            
        } catch (Exception e) {
            log.error("创建降级会话内存Advisor也失败: {}", e.getMessage(), e);
            return null; // 最终降级：不使用记忆
        }
    }
}
