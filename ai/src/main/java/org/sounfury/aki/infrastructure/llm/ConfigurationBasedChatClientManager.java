package org.sounfury.aki.infrastructure.llm;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.advisor.config.AdvisorConfiguration;
import org.sounfury.aki.domain.chatsession.ChatMode;
import org.sounfury.aki.domain.llm.service.AdvisorAwareChatClientManager;
import org.sounfury.aki.domain.llm.service.ChatClientManager;
import org.sounfury.aki.infrastructure.advisor.SpringAiAdvisorAssembler;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 基于配置的ChatClient管理器实现
 * 将Advisor配置转换为Spring AI的ChatClient
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ConfigurationBasedChatClientManager implements AdvisorAwareChatClientManager {
    
    private final ChatClientManager baseChatClientManager;
    private final SpringAiAdvisorAssembler advisorAssembler;
    private final MessageChatMemoryAdvisor messageChatMemoryAdvisor;
    
    @Override
    public ChatClient createChatClient(AdvisorConfiguration config, ChatMode mode) {
        return createChatClient(config, mode, List.of());
    }
    
    @Override
    public ChatClient createChatClient(AdvisorConfiguration config, ChatMode mode, List<Object> extraTools) {
        log.debug("创建基于配置的ChatClient，模式: {}, 配置: {}", mode, config);
        
        try {
            // 1. 组装基础Advisor
            List<Advisor> advisors = advisorAssembler.assembleAdvisors(config);
            
            // 2. 根据配置添加记忆Advisor
            if (config.isMemoryFullyConfigured() && supportsMemory(mode)) {
                advisors.add(messageChatMemoryAdvisor);
                log.debug("添加记忆Advisor，设置: {}", config.getMemorySettings());
            }
            
            // 3. 根据模式获取工具
            List<Object> allTools = new ArrayList<>(extraTools);
            if (supportsTools(mode)) {
                List<Object> modeTools = getToolsForMode(mode);
                allTools.addAll(modeTools);
                log.debug("模式 {} 添加 {} 个工具", mode, modeTools.size());
            }
            
            // 4. 创建ChatClient
            ChatClient chatClient = baseChatClientManager.getChatClientWithAdvisorsAndTools(advisors, allTools);
            
            log.debug("ChatClient创建成功，Advisor数量: {}, 工具数量: {}", 
                    advisors.size(), allTools.size());
            
            return chatClient;
            
        } catch (Exception e) {
            log.error("创建ChatClient失败，模式: {}", mode, e);
            throw new RuntimeException("创建ChatClient失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 检查模式是否支持记忆
     */
    private boolean supportsMemory(ChatMode mode) {
        return switch (mode) {
            case AGENT, CONVERSATION -> true;
            default -> false;
        };
    }
    
    /**
     * 检查模式是否支持工具
     */
    private boolean supportsTools(ChatMode mode) {
        return switch (mode) {
            case AGENT -> true;
            default -> false;
        };
    }
    
    /**
     * 根据模式获取工具列表
     */
    private List<Object> getToolsForMode(ChatMode mode) {
        return switch (mode) {
            case AGENT -> getAgentTools();
            default -> List.of();
        };
    }
    
    /**
     * 获取Agent模式的工具
     */
    private List<Object> getAgentTools() {
        // TODO: 从工具提供者获取Agent工具
        // return toolProvider.getAgentTools();
        
        // 暂时返回空列表，等工具系统完善后再实现
        log.debug("Agent工具暂未实现，返回空列表");
        return List.of();
    }
}
