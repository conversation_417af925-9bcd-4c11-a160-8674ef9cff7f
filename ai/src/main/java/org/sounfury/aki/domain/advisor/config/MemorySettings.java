package org.sounfury.aki.domain.advisor.config;

import lombok.Builder;
import lombok.Data;

/**
 * 记忆相关设置
 * 包含记忆功能的所有配置参数
 */
@Data
@Builder
public class MemorySettings {
    
    /**
     * 最大历史记录数量
     */
    @Builder.Default
    private int maxHistorySize = 20;
    
    /**
     * 是否启用长期记忆
     */
    @Builder.Default
    private boolean enableLongTermMemory = false;
    
    /**
     * 记忆保留天数
     */
    @Builder.Default
    private int retentionDays = 30;
    
    /**
     * 是否启用记忆压缩
     * 当历史记录过长时，是否自动压缩旧的对话
     */
    @Builder.Default
    private boolean enableCompression = true;
    
    /**
     * 压缩触发阈值
     * 当历史记录超过此数量时触发压缩
     */
    @Builder.Default
    private int compressionThreshold = 50;
    
    /**
     * 自定义记忆提示词模板（可选）
     */
    private String customMemoryTemplate;
    
    /**
     * 检查设置是否有效
     */
    public boolean isValid() {
        return maxHistorySize > 0 
               && maxHistorySize <= 1000  // 限制最大历史数量
               && retentionDays > 0 
               && retentionDays <= 365    // 限制最大保留天数
               && compressionThreshold > maxHistorySize; // 压缩阈值应大于最大历史数量
    }
    
    /**
     * 检查是否有自定义记忆模板
     */
    public boolean hasCustomMemoryTemplate() {
        return customMemoryTemplate != null && !customMemoryTemplate.trim().isEmpty();
    }
}
