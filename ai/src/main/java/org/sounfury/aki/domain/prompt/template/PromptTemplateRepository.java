package org.sounfury.aki.domain.prompt.template;

import java.util.List;
import java.util.Optional;

/**
 * 提示词模板仓储接口
 * 定义提示词模板聚合的持久化操作
 */
public interface PromptTemplateRepository {
    
    /**
     * 根据ID查找模板
     * @param id 模板ID
     * @return 模板实体，如果不存在则返回空
     */
    Optional<PromptTemplate> findById(TemplateId id);
    
    /**
     * 根据类型查找所有启用的模板
     * @param type 模板类型
     * @return 启用的模板列表
     */
    List<PromptTemplate> findByTypeAndEnabled(TemplateType type, boolean enabled);
    
    /**
     * 根据类型查找所有模板
     * @param type 模板类型
     * @return 模板列表
     */
    List<PromptTemplate> findByType(TemplateType type);
    
    /**
     * 根据名称查找模板
     * @param name 模板名称
     * @return 模板实体，如果不存在则返回空
     */
    Optional<PromptTemplate> findByName(String name);
    
    /**
     * 查找所有启用的模板
     * @return 启用的模板列表
     */
    List<PromptTemplate> findAllEnabled();
    
    /**
     * 查找所有模板
     * @return 所有模板列表
     */
    List<PromptTemplate> findAll();
    
    /**
     * 保存模板
     * @param template 模板实体
     * @return 保存后的模板实体
     */
    PromptTemplate save(PromptTemplate template);
    
    /**
     * 删除模板
     * @param id 模板ID
     */
    void deleteById(TemplateId id);
    
    /**
     * 检查模板是否存在
     * @param id 模板ID
     * @return 如果存在返回true，否则返回false
     */
    boolean existsById(TemplateId id);
    
    /**
     * 检查模板名称是否存在
     * @param name 模板名称
     * @return 如果存在返回true，否则返回false
     */
    boolean existsByName(String name);
    
    /**
     * 根据类型和名称查找模板
     * @param type 模板类型
     * @param name 模板名称
     * @return 模板实体，如果不存在则返回空
     */
    Optional<PromptTemplate> findByTypeAndName(TemplateType type, String name);
}
