package org.sounfury.aki.domain.prompt.template;

/**
 * 提示词模板类型枚举
 * 按功能领域分组，分为大组和小组
 */
public enum TemplateType {
    
    // ========== 系统基础大组 ==========
    /**
     * 系统提示词模板
     * 例如："你是世界一流的演员"
     */
    SYSTEM_PROMPT("system_prompt", "系统提示词", "定义AI的基础角色和能力"),
    
    // ========== 角色相关大组 ==========
    /**
     * 角色介绍模板
     * 例如："你扮演的角色是{character_name}"
     */
    CHARACTER_INTRO("character_intro", "角色介绍", "介绍角色的基本信息"),
    
    /**
     * 性格描述模板
     * 例如："你的性格是{personality}"
     */
    PERSONALITY_PROMPT("personality_prompt", "性格描述", "描述角色的性格特征"),
    
    /**
     * 场景设定模板
     * 例如："当前场景是{scenario}"
     */
    SCENARIO_PROMPT("scenario_prompt", "场景设定", "描述当前的世界设定和场景"),
    
    /**
     * 示例对话模板
     * 例如："以下是示例对话：{examples}"
     */
    EXAMPLE_DIALOGUE("example_dialogue", "示例对话", "提供对话示例"),
    
    // ========== 用户交互大组 ==========
    /**
     * 用户称呼模板
     * 例如："用户的名字是{user_name}，你可以称呼他为{user_name}"
     */
    USER_ADDRESS("user_address", "用户称呼", "定义如何称呼用户"),
    
    // ========== 行为指导大组 ==========
    /**
     * 对话行为指导模板
     * 针对普通对话场景的行为指导
     */
    CONVERSATION_BEHAVIOR("conversation_behavior", "对话行为指导", "指导AI在对话中的行为规范"),

    /**
     * 任务行为指导模板
     * 针对特定任务执行的行为指导
     */
    TASK_BEHAVIOR("task_behavior", "任务行为指导", "指导AI在执行特定任务时的行为规范"),

    /**
     * Agent行为指导模板
     * 针对工具调用场景的行为指导
     */
    AGENT_BEHAVIOR("agent_behavior", "Agent行为指导", "指导AI在使用工具时的行为规范"),

    // ========== 任务大组 ==========
    // --- 文章任务小组 ---
    /**
     * 文章总结任务模板
     */
    ARTICLE_SUMMARY("article_summary", "文章总结", "指导AI进行文章总结的专用模板"),

    /**
     * 文章摘录任务模板
     */
    ARTICLE_EXCERPT("article_excerpt", "文章摘录", "指导AI进行文章摘录的专用模板"),

    // --- 陪伴任务小组 ---
    /**
     * 发布祝贺任务模板
     */
    PUBLISH_CONGRATULATION("publish_congratulation", "发布祝贺", "指导AI生成发布祝贺话语的专用模板"),

    /**
     * 登录欢迎任务模板
     */
    LOGIN_WELCOME("login_welcome", "登录欢迎", "指导AI生成登录欢迎话语的专用模板");
    
    private final String code;
    private final String name;
    private final String description;
    
    TemplateType(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取模板类型
     */
    public static TemplateType fromCode(String code) {
        for (TemplateType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的模板类型代码: " + code);
    }
}
