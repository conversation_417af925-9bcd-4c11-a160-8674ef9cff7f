package org.sounfury.aki.domain.prompt.advisor;

import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.common.AbstractDomainAdvisor;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;

/**
 * 系统全局提示词领域Advisor
 * 提供基础的系统提示词
 */
@Slf4j
public class SystemPromptDomainAdvisor extends AbstractDomainAdvisor {

    public SystemPromptDomainAdvisor(SystemPromptManager systemPromptManager) {
        super(systemPromptManager);
    }

    @Override
    public String getName() {
        return "SystemPromptAdvisor";
    }

    @Override
    public int getOrder() {
        return 100; // 最高优先级，最先执行
    }

    @Override
    public String buildPrompt() {
        log.debug("构建系统提示词");

        String systemPrompt = systemPromptManager.buildBaseSystemPrompt();

        if (isEmpty(systemPrompt)) {
            log.warn("系统提示词为空");
            return null;
        }

        log.debug("系统提示词构建完成: {}", safeSubstring(systemPrompt, 50));
        return systemPrompt;
    }
}
