package org.sounfury.aki.domain.character.advisor;

import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.common.DomainAdvisor;
import org.sounfury.aki.domain.prompt.template.service.CharacterPromptManager;

/**
 * 角色卡领域Advisor
 * 提供角色扮演的提示词
 */
@Slf4j
public class CharacterCardDomainAdvisor implements DomainAdvisor {

    private final CharacterPromptManager characterPromptManager;
    private final String characterId;

    public CharacterCardDomainAdvisor(CharacterPromptManager characterPromptManager, String characterId) {
        this.characterPromptManager = characterPromptManager;
        this.characterId = characterId;
    }

    @Override
    public String getName() {
        return "CharacterCardAdvisor";
    }

    @Override
    public int getOrder() {
        return 200; // 在系统提示词之后
    }

    @Override
    public String buildPrompt() {
        log.debug("构建角色卡提示词，角色ID: {}", characterId);

        if (characterId == null || characterId.trim().isEmpty()) {
            log.debug("角色ID为空，跳过角色卡提示词");
            return null;
        }

        String characterPrompt = characterPromptManager.buildCompleteCharacterPrompt(characterId);

        if (characterPrompt == null || characterPrompt.trim().isEmpty()) {
            log.warn("角色ID {} 的角色卡提示词为空", characterId);
            return null;
        }

        log.debug("角色卡提示词构建完成: {}", characterPrompt.substring(0, Math.min(50, characterPrompt.length())));
        return characterPrompt;
    }

    /**
     * 获取角色ID
     * @return 角色ID
     */
    public String getCharacterId() {
        return characterId;
    }
}
