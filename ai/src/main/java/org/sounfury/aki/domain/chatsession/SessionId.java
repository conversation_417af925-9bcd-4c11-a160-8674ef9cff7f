package org.sounfury.aki.domain.chatsession;

import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.UUID;

/**
 * 会话ID值对象
 * 确保会话ID的不可变性和有效性
 */
@Getter
@EqualsAndHashCode
public class SessionId {
    
    private final String value;
    
    private SessionId(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("会话ID不能为空");
        }
        this.value = value.trim();
    }
    
    /**
     * 从字符串创建会话ID
     * @param value 会话ID字符串
     * @return SessionId实例
     */
    public static SessionId of(String value) {
        return new SessionId(value);
    }
    
    /**
     * 生成新的会话ID
     * @return 新的SessionId实例
     */
    public static SessionId generate() {
        return new SessionId(UUID.randomUUID().toString());
    }
    
    /**
     * 检查是否为有效的会话ID格式
     * @param value 待检查的字符串
     * @return 是否有效
     */
    public static boolean isValid(String value) {
        return value != null && !value.trim().isEmpty();
    }
    
    @Override
    public String toString() {
        return value;
    }
}
