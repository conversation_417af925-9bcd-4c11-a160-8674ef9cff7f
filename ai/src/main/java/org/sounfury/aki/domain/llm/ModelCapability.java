package org.sounfury.aki.domain.llm;

/**
 * 模型能力枚举
 * 只定义核心的模型能力
 */
public enum ModelCapability {

    /**
     * 函数调用能力（Function Call / Tool Use）
     * 模型能够理解和调用外部工具函数
     */
    FUNCTION_CALL("function_call", "函数调用", "支持调用外部工具和函数"),

    /**
     * 多模态能力（Vision）
     * 模型能够理解和分析图像内容
     */
    MULTIMODAL("multimodal", "多模态", "支持图像理解和分析");

    private final String code;
    private final String name;
    private final String description;

    ModelCapability(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取能力
     */
    public static ModelCapability fromCode(String code) {
        for (ModelCapability capability : values()) {
            if (capability.code.equals(code)) {
                return capability;
            }
        }
        throw new IllegalArgumentException("未知的模型能力代码: " + code);
    }
}
