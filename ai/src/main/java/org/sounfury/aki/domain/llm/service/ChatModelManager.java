package org.sounfury.aki.domain.llm.service;

import org.sounfury.aki.domain.llm.config.LlmConfiguration;
import org.springframework.ai.chat.model.ChatModel;

/**
 * 聊天模型领域服务接口
 * 管理ChatModel的生命周期
 */
public interface ChatModelManager {

    /**
     * 获取当前活跃的ChatModel实例
     * @return 当前ChatModel实例
     */
    ChatModel getCurrentChatModel();

    /**
     * 刷新当前ChatModel实例
     * 当配置发生变更时调用
     */
    void refreshChatModel();

    /**
     * 获取当前LLM配置
     * @return 当前LLM配置
     */
    LlmConfiguration getCurrentConfiguration();
}
