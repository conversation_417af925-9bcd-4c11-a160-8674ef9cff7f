package org.sounfury.aki.domain.chatsession;

import java.util.Optional;

/**
 * 会话仓储接口
 * 定义会话的持久化操作
 */
public interface SessionRepository {
    
    /**
     * 保存会话
     * @param session 会话对象
     */
    void save(ChatSession session);
    
    /**
     * 根据会话ID查找会话
     * @param sessionId 会话ID
     * @return 会话对象，如果不存在则返回空
     */
    Optional<ChatSession> findById(SessionId sessionId);
    
    /**
     * 检查会话是否存在
     * @param sessionId 会话ID
     * @return 是否存在
     */
    boolean exists(SessionId sessionId);
    
    /**
     * 删除会话
     * @param sessionId 会话ID
     */
    void deleteById(SessionId sessionId);
    
    /**
     * 获取会话总数
     * @return 会话总数
     */
    long count();
    
    /**
     * 清理过期会话
     * @param timeoutMinutes 超时分钟数
     * @return 清理的会话数量
     */
    int cleanupExpiredSessions(int timeoutMinutes);
}
