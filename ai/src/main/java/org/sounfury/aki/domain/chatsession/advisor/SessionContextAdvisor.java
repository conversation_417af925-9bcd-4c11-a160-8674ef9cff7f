package org.sounfury.aki.domain.chatsession.advisor;

import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.common.DomainAdvisor;
import org.sounfury.aki.domain.chatsession.ChatSession;

/**
 * 会话上下文领域Advisor
 * 提供会话的上下文信息，如对话轮次、会话模式等
 */
@Slf4j
public class SessionContextAdvisor implements DomainAdvisor {
    
    private final ChatSession session;
    private final boolean includeStats;
    
    /**
     * 构造函数
     * @param session 当前会话
     * @param includeStats 是否包含统计信息
     */
    public SessionContextAdvisor(ChatSession session, boolean includeStats) {
        this.session = session;
        this.includeStats = includeStats;
    }
    
    /**
     * 构造函数（默认包含统计信息）
     * @param session 当前会话
     */
    public SessionContextAdvisor(ChatSession session) {
        this(session, true);
    }
    
    @Override
    public String getName() {
        return "SessionContextAdvisor";
    }
    
    @Override
    public int getOrder() {
        return 180; // 在记忆Advisor之后
    }
    
    @Override
    public String buildPrompt() {
        log.debug("构建会话上下文提示词，会话ID: {}", session.getSessionId().getValue());
        
        StringBuilder contextPrompt = new StringBuilder();
        
        // 基本会话信息
        contextPrompt.append("当前会话信息：\n");
        contextPrompt.append(String.format("- 会话模式: %s\n", session.getMode().getName()));
        
        // 用户身份信息
        if (session.isOwnerSession()) {
            contextPrompt.append("- 用户身份: 站长（拥有完整权限）\n");
        } else {
            contextPrompt.append("- 用户身份: 访客（受限权限）\n");
        }
        
        // 功能启用状态
        if (session.isToolsEnabled()) {
            contextPrompt.append("- 工具调用: 已启用\n");
        }
        
        if (session.isMemoryEnabled()) {
            contextPrompt.append("- 记忆功能: 已启用\n");
        }
        
        // 统计信息
        if (includeStats) {
            contextPrompt.append(String.format("- 对话轮次: %d\n", session.getConversationRounds()));
            contextPrompt.append(String.format("- 消息数量: %d\n", session.getMessageCount()));
        }
        
        // 权限提示
        if (session.canUseAgentTools()) {
            contextPrompt.append("\n你可以使用Agent工具来帮助用户完成任务。");
        } else {
            contextPrompt.append("\n当前会话不支持工具调用，请专注于对话交流。");
        }
        
        String result = contextPrompt.toString();
        log.debug("会话上下文提示词构建完成，长度: {} 字符", result.length());
        return result;
    }
    
    /**
     * 获取会话模式
     */
    public String getSessionMode() {
        return session.getMode().getName();
    }
    
    /**
     * 检查是否为站长会话
     */
    public boolean isOwnerSession() {
        return session.isOwnerSession();
    }
    
    /**
     * 检查是否支持工具调用
     */
    public boolean supportsTools() {
        return session.canUseAgentTools();
    }
}
