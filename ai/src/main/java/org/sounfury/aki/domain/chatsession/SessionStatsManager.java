package org.sounfury.aki.domain.chatsession;

/**
 * 会话统计管理接口
 * 负责会话的统计和清理操作
 */
public interface SessionStatsManager {
    
    /**
     * 获取会话统计信息
     * @return 会话统计信息
     */
    SessionStats getSessionStats();
    
    /**
     * 清理过期会话
     * @param timeoutMinutes 超时分钟数
     * @return 清理的会话数量
     */
    int cleanupExpiredSessions(int timeoutMinutes);
    
    /**
     * 会话统计信息
     */
    record SessionStats(
            long totalSessions,
            long activeSessions
    ) {}
}
