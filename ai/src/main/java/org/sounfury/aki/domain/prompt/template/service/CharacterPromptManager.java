package org.sounfury.aki.domain.prompt.template.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.character.Character;
import org.sounfury.aki.domain.character.CharacterId;
import org.sounfury.aki.domain.character.CharacterRepository;
import org.sounfury.aki.domain.prompt.template.PromptTemplateRepository;
import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.StringJoiner;

/**
 * 角色提示词服务
 * 领域层的角色提示词服务，调用Repository接口
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CharacterPromptManager {

    private final PromptTemplateRepository templateRepository;
    private final CharacterRepository characterRepository;

    /**
     * 构建角色介绍提示词
     * @param character 角色信息
     * @return 角色介绍提示词
     */
    public String buildCharacterIntroPrompt(Character character) {
        if (!isValidCharacter(character)) {
            return "";
        }

        return templateRepository.findByTypeAndEnabled(TemplateType.CHARACTER_INTRO, true)
                .stream()
                .findFirst()
                .map(template -> {
                    Map<String, String> variables = new HashMap<>();
                    variables.put("character_name", character.getCard().getCharName());
                    variables.put("character_background", character.getDescription() != null ? 
                            character.getDescription() : "");
                    
                    String rendered = template.render(variables);
                    log.debug("构建角色介绍提示词: {}", character.getName());
                    return rendered;
                })
                .orElse("");
    }

    /**
     * 构建性格描述提示词
     * @param character 角色信息
     * @return 性格描述提示词
     */
    public String buildPersonalityPrompt(Character character) {
        if (!isValidCharacter(character)) {
            return "";
        }

        String personality = character.getCard().getCharPersona();
        if (personality == null || personality.trim().isEmpty()) {
            return "";
        }

        return templateRepository.findByTypeAndEnabled(TemplateType.PERSONALITY_PROMPT, true)
                .stream()
                .findFirst()
                .map(template -> {
                    Map<String, String> variables = Map.of("personality", personality);
                    String rendered = template.render(variables);
                    log.debug("构建性格描述提示词: {}", character.getName());
                    return rendered;
                })
                .orElse("");
    }

    /**
     * 构建场景设定提示词
     * @param character 角色信息
     * @return 场景设定提示词
     */
    public String buildScenarioPrompt(Character character) {
        if (!isValidCharacter(character)) {
            return "";
        }

        String scenario = character.getCard().getWorldScenario();
        if (scenario == null || scenario.trim().isEmpty()) {
            return "";
        }

        return templateRepository.findByTypeAndEnabled(TemplateType.SCENARIO_PROMPT, true)
                .stream()
                .findFirst()
                .map(template -> {
                    Map<String, String> variables = Map.of("scenario", scenario);
                    String rendered = template.render(variables);
                    log.debug("构建场景设定提示词: {}", character.getName());
                    return rendered;
                })
                .orElse("");
    }

    /**
     * 构建示例对话提示词
     * @param character 角色信息
     * @return 示例对话提示词
     */
    public String buildExampleDialoguePrompt(Character character) {
        if (!isValidCharacter(character)) {
            return "";
        }

        String examples = character.getCard().getExampleDialogue();
        if (examples == null || examples.trim().isEmpty()) {
            return "";
        }

        return templateRepository.findByTypeAndEnabled(TemplateType.EXAMPLE_DIALOGUE, true)
                .stream()
                .findFirst()
                .map(template -> {
                    Map<String, String> variables = Map.of("examples", examples);
                    String rendered = template.render(variables);
                    log.debug("构建示例对话提示词: {}", character.getName());
                    return rendered;
                })
                .orElse("");
    }

    /**
     * 构建完整的角色人格提示词
     * @param character 角色信息
     * @return 完整的角色人格提示词
     */
    public String buildCompleteCharacterPrompt(Character character) {
        if (!isValidCharacter(character)) {
            return "";
        }

        StringJoiner promptBuilder = new StringJoiner("\n\n");

        // 1. 添加角色介绍
        String intro = buildCharacterIntroPrompt(character);
        if (!intro.isEmpty()) {
            promptBuilder.add(intro);
        }

        // 2. 添加性格描述
        String personality = buildPersonalityPrompt(character);
        if (!personality.isEmpty()) {
            promptBuilder.add(personality);
        }

        // 3. 添加场景设定
        String scenario = buildScenarioPrompt(character);
        if (!scenario.isEmpty()) {
            promptBuilder.add(scenario);
        }

        // 4. 添加示例对话
        String examples = buildExampleDialoguePrompt(character);
        if (!examples.isEmpty()) {
            promptBuilder.add(examples);
        }

        String result = promptBuilder.toString();
        log.debug("构建完整角色提示词: {}, 长度: {}", character.getName(), result.length());

        return result;
    }

    /**
     * 构建完整的角色人格提示词（通过角色ID）
     * @param characterId 角色ID
     * @return 完整的角色人格提示词
     */
    public String buildCompleteCharacterPrompt(String characterId) {
        if (characterId == null || characterId.trim().isEmpty()) {
            log.debug("角色ID为空，返回空提示词");
            return "";
        }

        try {
            Character character = characterRepository.findById(CharacterId.of(characterId))
                    .orElse(null);

            if (character == null) {
                log.warn("未找到角色ID: {}, 返回空提示词", characterId);
                return "";
            }

            return buildCompleteCharacterPrompt(character);

        } catch (Exception e) {
            log.error("获取角色失败，角色ID: {}, 错误: {}", characterId, e.getMessage());
            return "";
        }
    }

    /**
     * 检查角色是否有效
     * @param character 角色信息
     * @return 是否有效
     */
    public boolean isValidCharacter(Character character) {
        return character != null && character.isAvailable();
    }
}
