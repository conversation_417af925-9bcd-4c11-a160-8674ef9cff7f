package org.sounfury.aki.domain.llm.service;

import org.sounfury.aki.domain.advisor.config.AdvisorConfiguration;
import org.sounfury.aki.domain.chatsession.ChatMode;
import org.springframework.ai.chat.client.ChatClient;

import java.util.List;

/**
 * 支持Advisor的ChatClient管理器
 * 为策略类提供便捷的基于配置的ChatClient创建方法
 */
public interface AdvisorAwareChatClientManager {

    /**
     * 基于配置创建ChatClient
     * 这是策略类的主要入口方法
     * @param config Advisor配置
     * @param mode 聊天模式
     * @return 配置好的ChatClient
     */
    ChatClient createChatClient(AdvisorConfiguration config, ChatMode mode);

    /**
     * 基于配置和工具创建ChatClient
     * 完全控制的创建方法
     * @param config Advisor配置
     * @param mode 聊天模式
     * @param tools 额外工具列表
     * @return 配置好的ChatClient
     */
    ChatClient createChatClient(AdvisorConfiguration config, ChatMode mode, List<Object> tools);
}
