package org.sounfury.aki.domain.llm.service;

import org.sounfury.aki.domain.common.DomainAdvisor;
import org.sounfury.aki.domain.chatsession.ChatMode;
import org.springframework.ai.chat.client.ChatClient;

import java.util.List;

/**
 * 支持Advisor的ChatClient管理器
 * 为策略类提供便捷的Advisor驱动的ChatClient创建方法
 */
public interface AdvisorAwareChatClientManager {
    
    /**
     * 创建基于Advisor的ChatClient
     * 这是策略类的主要入口方法
     * @param advisors DomainAdvisor列表
     * @return 配置了Advisor的ChatClient
     */
    ChatClient createChatClient(List<DomainAdvisor> advisors);
    
    /**
     * 创建基于Advisor和模式的ChatClient
     * 根据模式自动添加工具支持
     * @param advisors DomainAdvisor列表
     * @param mode 聊天模式
     * @return 配置了Advisor和可能工具的ChatClient
     */
    ChatClient createChatClient(List<DomainAdvisor> advisors, ChatMode mode);
    
    /**
     * 创建基于Advisor、模式和工具的ChatClient
     * 完全控制的创建方法
     * @param advisors DomainAdvisor列表
     * @param mode 聊天模式
     * @param tools 工具列表
     * @return 配置了Advisor和工具的ChatClient
     */
    ChatClient createChatClient(List<DomainAdvisor> advisors, ChatMode mode, List<Object> tools);
}
