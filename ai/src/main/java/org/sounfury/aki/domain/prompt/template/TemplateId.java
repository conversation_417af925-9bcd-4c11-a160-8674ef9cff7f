package org.sounfury.aki.domain.prompt.template;

import lombok.Value;

import java.util.Objects;

/**
 * 模板ID值对象
 * 作为提示词模板聚合的唯一标识
 */
@Value
public class TemplateId {
    String value;
    
    public TemplateId(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("模板ID不能为空");
        }
        this.value = value.trim();
    }
    
    public static TemplateId of(String value) {
        return new TemplateId(value);
    }
    
    /**
     * 根据模板类型和名称生成ID
     */
    public static TemplateId generate(TemplateType type, String name) {
        if (type == null) {
            throw new IllegalArgumentException("模板类型不能为空");
        }
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("模板名称不能为空");
        }
        return new TemplateId(type.getCode() + "_" + name.trim().toLowerCase().replaceAll("\\s+", "_"));
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TemplateId that = (TemplateId) o;
        return Objects.equals(value, that.value);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(value);
    }
    
    @Override
    public String toString() {
        return value;
    }
}
