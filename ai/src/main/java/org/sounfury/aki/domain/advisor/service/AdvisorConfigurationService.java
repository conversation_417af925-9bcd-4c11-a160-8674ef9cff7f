package org.sounfury.aki.domain.advisor.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.advisor.config.AdvisorConfiguration;
import org.sounfury.aki.domain.advisor.config.MemorySettings;
import org.sounfury.aki.domain.advisor.config.RagSettings;
import org.sounfury.aki.domain.chatsession.ChatMode;
import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.sounfury.aki.domain.service.conversation.strategy.ConversationStrategy;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Advisor配置服务
 * 负责根据业务需求构建Advisor配置，纯领域逻辑，无技术依赖
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdvisorConfigurationService {
    
    // TODO: 注入世界书管理器，暂时先用占位符
    // private final WorldBookManager worldBookManager;
    
    /**
     * 根据对话请求构建Advisor配置
     * @param request 对话请求
     * @return Advisor配置
     */
    public AdvisorConfiguration buildConfiguration(ConversationStrategy.ConversationRequest request) {
        log.debug("构建Advisor配置，会话ID: {}, 模式: {}", 
                request.session().getSessionId().getValue(), 
                request.session().getMode());
        
        return AdvisorConfiguration.builder()
                .characterId(request.session().getCharacterId())
                .userName(request.userName())
                .enableRag(shouldEnableRag(request))
                .enableMemory(shouldEnableMemory(request))
                .behaviorType(determineBehaviorType(request))
                .ragSettings(buildRagSettings(request))
                .memorySettings(buildMemorySettings(request))
                .build();
    }
    
    /**
     * 判断是否应该启用RAG功能
     */
    private boolean shouldEnableRag(ConversationStrategy.ConversationRequest request) {
        // 业务规则：只有Agent模式和Chat模式支持RAG
        ChatMode mode = request.session().getMode();
        if (mode != ChatMode.AGENT && mode != ChatMode.CONVERSATION) {
            log.debug("模式 {} 不支持RAG功能", mode);
            return false;
        }
        
        // 检查角色是否有绑定的世界书
        String characterId = request.session().getCharacterId();
        if (characterId == null || characterId.trim().isEmpty()) {
            log.debug("角色ID为空，不启用RAG功能");
            return false;
        }
        
        // TODO: 调用世界书管理器检查是否启用
        // return worldBookManager.isEnabled(characterId);
        
        // 暂时返回false，等世界书功能实现后再修改
        log.debug("世界书功能暂未实现，不启用RAG");
        return false;
    }
    
    /**
     * 判断是否应该启用记忆功能
     */
    private boolean shouldEnableMemory(ConversationStrategy.ConversationRequest request) {
        // 业务规则：Agent模式和Chat模式支持记忆
        ChatMode mode = request.session().getMode();
        boolean supportsMemory = mode == ChatMode.AGENT || mode == ChatMode.CONVERSATION;
        
        log.debug("模式 {} 记忆支持: {}", mode, supportsMemory);
        return supportsMemory;
    }
    
    /**
     * 确定行为指导模板类型
     */
    private TemplateType determineBehaviorType(ConversationStrategy.ConversationRequest request) {
        ChatMode mode = request.session().getMode();
        
        return switch (mode) {
            case AGENT -> TemplateType.AGENT_BEHAVIOR;
            case CONVERSATION -> TemplateType.CONVERSATION_BEHAVIOR;
            default -> {
                log.debug("模式 {} 使用默认行为模板", mode);
                yield TemplateType.CONVERSATION_BEHAVIOR;
            }
        };
    }
    
    /**
     * 构建RAG设置
     */
    private RagSettings buildRagSettings(ConversationStrategy.ConversationRequest request) {
        if (!shouldEnableRag(request)) {
            return null;
        }
        
        // TODO: 根据角色和世界书配置构建RAG设置
        // List<String> enabledWorldBooks = worldBookManager.getEnabledWorldBooks(request.session().getCharacterId());
        
        return RagSettings.builder()
                .similarityThreshold(0.8)
                .topK(5)
                .allowEmptyContext(false)
                .enabledWorldBooks(List.of()) // 暂时为空
                .build();
    }
    
    /**
     * 构建记忆设置
     */
    private MemorySettings buildMemorySettings(ConversationStrategy.ConversationRequest request) {
        if (!shouldEnableMemory(request)) {
            return null;
        }
        
        ChatMode mode = request.session().getMode();
        
        // 根据模式调整记忆设置
        return switch (mode) {
            case AGENT -> MemorySettings.builder()
                    .maxHistorySize(30)  // Agent模式需要更多上下文
                    .enableLongTermMemory(true)
                    .enableCompression(true)
                    .compressionThreshold(60)
                    .retentionDays(30)
                    .build();
                    
            case CONVERSATION -> MemorySettings.builder()
                    .maxHistorySize(20)  // 普通聊天模式
                    .enableLongTermMemory(false)
                    .enableCompression(true)
                    .compressionThreshold(40)
                    .retentionDays(7)
                    .build();
        };
    }
}
