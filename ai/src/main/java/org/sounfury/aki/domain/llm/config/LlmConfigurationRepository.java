package org.sounfury.aki.domain.llm.config;

import java.util.Optional;

/**
 * LLM配置仓储接口
 * 定义LLM配置的持久化操作
 */
public interface LlmConfigurationRepository {
    
    /**
     * 保存配置
     * @param configuration LLM配置
     * @return 保存后的配置
     */
    LlmConfiguration save(LlmConfiguration configuration);
    
    /**
     * 根据配置ID查找配置
     * @param configurationId 配置ID
     * @return 配置信息，如果不存在则返回空
     */
    Optional<LlmConfiguration> findById(String configurationId);
    
    /**
     * 获取当前全局配置
     * @return 当前全局配置
     */
    Optional<LlmConfiguration> findGlobalConfiguration();
    
    /**
     * 删除配置
     * @param configurationId 配置ID
     */
    void deleteById(String configurationId);
    
    /**
     * 检查配置是否存在
     * @param configurationId 配置ID
     * @return true表示存在，false表示不存在
     */
    boolean existsById(String configurationId);
}
