package org.sounfury.aki.domain.common;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;

import java.util.Map;

/**
 * DomainAdvisor抽象基类
 * 提供公共的模板处理逻辑，减少重复代码
 */
@Slf4j
@RequiredArgsConstructor
public abstract class AbstractDomainAdvisor implements DomainAdvisor {
    
    protected final SystemPromptManager systemPromptManager;
    
    /**
     * 从模板构建提示词
     * @param templateType 模板类型
     * @param variables 模板变量
     * @return 构建的提示词内容
     */
    protected String buildFromTemplate(TemplateType templateType, Map<String, Object> variables) {
        try {
            return systemPromptManager.buildPrompt(templateType, variables);
        } catch (Exception e) {
            log.error("构建模板 {} 失败: {}", templateType.getName(), e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 从模板构建提示词（无变量版本）
     * @param templateType 模板类型
     * @return 构建的提示词内容
     */
    protected String buildFromTemplate(TemplateType templateType) {
        return buildFromTemplate(templateType, Map.of());
    }
    
    /**
     * 检查字符串是否为空
     * @param str 待检查的字符串
     * @return 是否为空
     */
    protected boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 安全地截取字符串用于日志
     * @param str 原字符串
     * @param maxLength 最大长度
     * @return 截取后的字符串
     */
    protected String safeSubstring(String str, int maxLength) {
        if (isEmpty(str)) {
            return "";
        }
        return str.length() > maxLength ? str.substring(0, maxLength) + "..." : str;
    }
}
