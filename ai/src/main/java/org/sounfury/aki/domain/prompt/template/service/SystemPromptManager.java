package org.sounfury.aki.domain.prompt.template.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.prompt.template.PromptTemplateRepository;
import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 系统提示词服务
 * 领域层的系统提示词服务，调用Repository接口
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SystemPromptManager {

    private final PromptTemplateRepository templateRepository;

    /**
     * 构建基础系统提示词
     * @return 系统基础提示词
     */
    public String buildBaseSystemPrompt() {
        return templateRepository.findByTypeAndEnabled(TemplateType.SYSTEM_PROMPT, true)
                .stream()
                .findFirst()
                .map(template -> {
                    String rendered = template.render(Map.of());
                    log.debug("构建基础系统提示词: {}", template.getName());
                    return rendered;
                })
                .orElse("");
    }

    /**
     * 构建用户称呼提示词
     * @param userName 用户名称
     * @return 用户称呼提示词
     */
    public String buildUserAddressPrompt(String userName) {
        if (userName == null || userName.trim().isEmpty()) {
            return "";
        }

        return templateRepository.findByTypeAndEnabled(TemplateType.USER_ADDRESS, true)
                .stream()
                .findFirst()
                .map(template -> {
                    Map<String, String> variables = Map.of("user_name", userName.trim());
                    String rendered = template.render(variables);
                    log.debug("构建用户称呼提示词: {}", userName);
                    return rendered;
                })
                .orElse("");
    }

    /**
     * 构建行为指导提示词
     * @param behaviorType 行为指导类型
     * @return 行为指导提示词
     */
    public String buildBehaviorGuidePrompt(TemplateType behaviorType) {
        return templateRepository.findByTypeAndEnabled(behaviorType, true)
                .stream()
                .findFirst()
                .map(template -> {
                    String rendered = template.render(Map.of());
                    log.debug("构建行为指导提示词: {}", behaviorType.getName());
                    return rendered;
                })
                .orElse("");
    }
    /**
     * 构建任务特定提示词
     * @param taskCode 任务代码
     * @return 任务特定提示词
     */
    public String buildTaskSpecificPrompt(String taskCode) {
        TemplateType taskTemplateType = getTaskTemplateType(taskCode);
        if (taskTemplateType == null) {
            log.debug("未找到任务 {} 对应的特定模板", taskCode);
            return "";
        }

        return templateRepository.findByTypeAndEnabled(taskTemplateType, true)
                .stream()
                .findFirst()
                .map(template -> {
                    String rendered = template.render(Map.of());
                    log.debug("构建任务特定提示词: {} -> {}", taskCode, taskTemplateType.getName());
                    return rendered;
                })
                .orElse("");
    }


    /**
     * 通用的模板构建方法
     * @param templateType 模板类型
     * @param variables 模板变量
     * @return 构建的提示词内容
     */
    public String buildPrompt(TemplateType templateType, Map<String, Object> variables) {
        return templateRepository.findByTypeAndEnabled(templateType, true)
                .stream()
                .findFirst()
                .map(template -> {
                    // 将Map<String, Object>转换为Map<String, String>
                    Map<String, String> stringVariables = convertToStringMap(variables);
                    String rendered = template.render(stringVariables);
                    log.debug("构建提示词: {} -> {}", templateType.getName(), template.getName());
                    return rendered;
                })
                .orElse("");
    }

    /**
     * 将Map<String, Object>转换为Map<String, String>
     * @param variables 原始变量Map
     * @return 转换后的字符串Map
     */
    private Map<String, String> convertToStringMap(Map<String, Object> variables) {
        if (variables == null || variables.isEmpty()) {
            return Map.of();
        }

        return variables.entrySet().stream()
                .collect(java.util.stream.Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> entry.getValue() != null ? entry.getValue().toString() : ""
                ));
    }

    /**
     * 根据任务代码获取对应的模板类型
     * @param taskCode 任务代码
     * @return 对应的模板类型，如果不存在则返回null
     */
    public TemplateType getTaskTemplateType(String taskCode) {
        if (taskCode == null || taskCode.trim().isEmpty()) {
            return null;
        }

        return switch (taskCode.toLowerCase().trim()) {
            case "article_summary" -> TemplateType.ARTICLE_SUMMARY;
            case "article_excerpt" -> TemplateType.ARTICLE_EXCERPT;
            case "publish_congratulation" -> TemplateType.PUBLISH_CONGRATULATION;
            case "login_welcome" -> TemplateType.LOGIN_WELCOME;
            default -> null;
        };
    }
}
