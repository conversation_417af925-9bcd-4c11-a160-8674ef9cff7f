package org.sounfury.aki.domain.chatsession.service;

import org.sounfury.aki.domain.chatsession.ChatMode;
import org.sounfury.aki.domain.chatsession.ChatSession;
import org.sounfury.aki.domain.chatsession.SessionId;

import java.util.Optional;

/**
 * 会话管理接口
 * 负责会话的基本CRUD操作
 */
public interface SessionManager {
    
    /**
     * 创建新的聊天会话
     * @param sessionId 会话ID
     * @param mode 聊天模式
     * @param characterId 角色ID
     * @param isOwnerSession 是否是站长会话
     * @return 聊天会话
     */
    ChatSession createSession(SessionId sessionId, ChatMode mode, String characterId, boolean isOwnerSession);

    /**
     * 创建站长会话
     * @param sessionId 会话ID
     * @param mode 聊天模式
     * @param characterId 角色ID
     * @return 站长聊天会话
     */
    default ChatSession createOwnerSession(SessionId sessionId, ChatMode mode, String characterId) {
        return createSession(sessionId, mode, characterId, true);
    }

    /**
     * 创建游客会话
     * @param sessionId 会话ID
     * @param mode 聊天模式
     * @param characterId 角色ID
     * @return 游客聊天会话
     */
    default ChatSession createGuestSession(SessionId sessionId, ChatMode mode, String characterId) {
        return createSession(sessionId, mode, characterId, false);
    }

     boolean resetConversationRounds(SessionId sessionId) ;
    
    /**
     * 获取聊天会话
     * @param sessionId 会话ID
     * @return 聊天会话，如果不存在则返回空
     */
    Optional<ChatSession> getSession(SessionId sessionId);
    
    /**
     * 更新会话的最后活跃时间
     * @param sessionId 会话ID
     * @return 是否更新成功
     */
    boolean updateLastActiveTime(SessionId sessionId);
    
    /**
     * 检查会话是否存在
     * @param sessionId 会话ID
     * @return 是否存在
     */
    boolean sessionExists(SessionId sessionId);
    
    /**
     * 删除会话
     * @param sessionId 会话ID
     * @return 是否删除成功
     */
    boolean deleteSession(SessionId sessionId);
}
