package org.sounfury.aki.domain.service.task.strategy;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.application.task.dto.BaseTaskRequest;
import org.sounfury.aki.domain.llm.service.ChatClientManager;
import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;
import org.sounfury.aki.domain.prompt.template.service.CharacterPromptManager;
import org.sounfury.core.utils.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;

/**
 * 抽象任务策略模板类
 * 提供通用的任务执行流程，子类只需实现特定的业务逻辑
 */
@Slf4j
@RequiredArgsConstructor
public abstract class AbstractTaskStrategy implements TaskStrategy {
    
    protected final ChatClientManager chatClientManager;
    protected final SystemPromptManager systemPromptManager;
    protected final CharacterPromptManager characterPromptManager;
    
    @Override
    public final TaskResult execute(BaseTaskRequest request) {
        LocalDateTime startTime = LocalDateTime.now();
        log.info("开始执行{}任务，任务模式: {}", getStrategyName(), request.getTaskMode());
        
        try {
            // 1. 验证请求类型
            if (!isValidRequest(request)) {
                return TaskResult.failure("请求类型错误，期望" + getExpectedRequestType(), getStrategyName());
            }
            
            // 2. 获取用户输入内容（由子类实现）
            String userInput = getUserInput(request);
            if (userInput == null || userInput.trim().isEmpty()) {
                return TaskResult.failure("无法获取输入内容", getStrategyName());
            }
            
            // 3. 构建系统提示词
            String systemPrompt = buildSystemPrompt(request);
            
            // 4. 构建消息列表
            List<Message> messages = buildMessages(userInput, systemPrompt);
            
            // 5. 创建Prompt
            Prompt prompt = new Prompt(messages);
            
            // 6. 获取ChatClient并调用
            ChatClient chatClient = chatClientManager.getBasicChatClient();
            ChatResponse chatResponse = chatClient.prompt(prompt).call().chatResponse();
            String response = chatResponse.getResult().getOutput().getText();
            
            log.info("{}任务执行完成，任务模式: {}, 耗时: {}ms", 
                    getStrategyName(), request.getTaskMode(),
                    java.time.Duration.between(startTime, LocalDateTime.now()).toMillis());
            
            return TaskResult.success(response, getStrategyName());
            
        } catch (Exception e) {
            log.error("{}任务执行失败，任务模式: {}", getStrategyName(), request.getTaskMode(), e);
            return TaskResult.failure(e.getMessage(), getStrategyName());
        }
    }
    
    @Override
    public final Flux<String> executeStream(BaseTaskRequest request) {
        log.info("开始执行流式{}任务，任务模式: {}", getStrategyName(), request.getTaskMode());
        
        try {
            // 1. 验证请求类型
            if (!isValidRequest(request)) {
                return Flux.error(new IllegalArgumentException("请求类型错误，期望" + getExpectedRequestType()));
            }
            
            // 2. 获取用户输入内容（由子类实现）
            String userInput = getUserInput(request);

            // 3. 构建系统提示词
            String systemPrompt = buildSystemPrompt(request);
            
            // 4. 构建消息列表
            List<Message> messages = buildMessages(userInput, systemPrompt);
            
            // 5. 创建Prompt
            Prompt prompt = new Prompt(messages);
            
            // 6. 获取ChatClient并调用流式接口
            ChatClient chatClient = chatClientManager.getBasicChatClient();
            
            return chatClient
                    .prompt(prompt)
                    .stream()
                    .content()
                    .doOnComplete(() -> {
                        log.info("流式{}任务执行完成，任务模式: {}", getStrategyName(), request.getTaskMode());
                    })
                    .doOnError(error -> {
                        log.error("流式{}任务执行失败，任务模式: {}", getStrategyName(), request.getTaskMode(), error);
                    });
            
        } catch (Exception e) {
            log.error("流式{}任务执行异常，任务模式: {}", getStrategyName(), request.getTaskMode(), e);
            return Flux.error(e);
        }
    }
    
    /**
     * 构建系统提示词（通用逻辑）
     */
    private String buildSystemPrompt(BaseTaskRequest request) {
        StringJoiner promptBuilder = new StringJoiner("\n\n");
        
        // 1. 基础系统提示词
        String basePrompt = systemPromptManager.buildBaseSystemPrompt();
        if (!basePrompt.isEmpty()) {
            promptBuilder.add(basePrompt);
        }
        
        // 2. 角色人格提示词（使用默认角色）
        //TODO:从配置表中读取默认角色ID，这里暂时先写死
        String characterPrompt = characterPromptManager.buildCompleteCharacterPrompt("bartender");
        if (!characterPrompt.isEmpty()) {
            promptBuilder.add(characterPrompt);
        }
        
        // 3. 用户称呼提示词
        String userPrompt = systemPromptManager.buildUserAddressPrompt(request.getUserName());
        if (!userPrompt.isEmpty()) {
            promptBuilder.add(userPrompt);
        }
        
        // 4. 任务行为指导
        String behaviorPrompt = systemPromptManager.buildBehaviorGuidePrompt(TemplateType.TASK_BEHAVIOR);
        if (!behaviorPrompt.isEmpty()) {
            promptBuilder.add(behaviorPrompt);
        }
        
        // 5. 任务特定提示词（根据任务模式获取对应模板）
        String taskPrompt = systemPromptManager.buildTaskSpecificPrompt(request.getTaskMode().getCode());
        if (!taskPrompt.isEmpty()) {
            promptBuilder.add(taskPrompt);
        }
        
        return promptBuilder.toString();
    }
    
    /**
     * 构建消息列表（通用逻辑）
     */
    private List<Message> buildMessages(String userInput, String systemPrompt) {
        List<Message> messages = new ArrayList<>();
        
        // 1. 添加系统消息
        if (!systemPrompt.isEmpty()) {
            messages.add(new SystemMessage(systemPrompt));
        }

        if(StringUtils.isEmpty(userInput)) {
            return messages;
        }
        // 2. 添加用户消息
        messages.add(new UserMessage(userInput));
        
        return messages;
    }
    
    // ========== 抽象方法，由子类实现 ==========
    
    /**
     * 验证请求类型是否正确
     * @param request 请求对象
     * @return 是否为有效请求
     */
    protected abstract boolean isValidRequest(BaseTaskRequest request);
    
    /**
     * 获取期望的请求类型名称（用于错误提示）
     * @return 请求类型名称
     */
    protected abstract String getExpectedRequestType();
    
    /**
     * 获取用户输入内容
     * @param request 请求对象
     * @return 用户输入内容
     */
    protected abstract String getUserInput(BaseTaskRequest request);
    
    /**
     * 获取策略名称
     * @return 策略名称
     */
    protected abstract String getStrategyName();
}
