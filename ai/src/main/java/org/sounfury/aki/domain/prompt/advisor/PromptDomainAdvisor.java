package org.sounfury.aki.domain.prompt.advisor;

import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.common.AbstractDomainAdvisor;
import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;
import org.sounfury.aki.domain.service.task.enums.TaskMode;


import java.util.Map;

/**
 * 通用提示词领域Advisor
 * 合并了UserAddress、BehaviorGuide、TaskSpecific等功能
 * 根据配置的模板类型和变量构建提示词
 */
@Slf4j
public class PromptDomainAdvisor extends AbstractDomainAdvisor {
    
    private final TemplateType templateType;
    private final Map<String, Object> variables;
    private final String advisorName;
    private final int order;
    
    /**
     * 构造函数
     * @param systemPromptManager 系统提示词管理器
     * @param templateType 模板类型
     * @param variables 模板变量
     * @param advisorName Advisor名称
     * @param order 执行顺序
     */
    public PromptDomainAdvisor(SystemPromptManager systemPromptManager, 
                              TemplateType templateType, 
                              Map<String, Object> variables,
                              String advisorName,
                              int order) {
        super(systemPromptManager);
        this.templateType = templateType;
        this.variables = variables != null ? variables : Map.of();
        this.advisorName = advisorName;
        this.order = order;
    }
    
    /**
     * 用户称呼Advisor的便捷构造方法
     */
    public static PromptDomainAdvisor createUserAddressAdvisor(SystemPromptManager systemPromptManager, String userName) {
        return new PromptDomainAdvisor(
            systemPromptManager,
            TemplateType.USER_ADDRESS,
            Map.of("userName", userName != null ? userName : "旅行者"),
            "UserAddressAdvisor",
            300
        );
    }
    
    /**
     * 行为指导Advisor的便捷构造方法
     */
    public static PromptDomainAdvisor createBehaviorGuideAdvisor(SystemPromptManager systemPromptManager, TemplateType behaviorType) {
        return new PromptDomainAdvisor(
            systemPromptManager,
            behaviorType,
            Map.of(),
            "BehaviorGuideAdvisor",
            400
        );
    }
    
    /**
     * 任务特定Advisor的便捷构造方法
     */
    public static PromptDomainAdvisor createTaskSpecificAdvisor(SystemPromptManager systemPromptManager, TaskMode taskMode) {
        TemplateType templateType = mapTaskModeToTemplate(taskMode);
        return new PromptDomainAdvisor(
            systemPromptManager,
            templateType,
            Map.of("taskMode", taskMode.getCode()),
            "TaskSpecificAdvisor",
            500
        );
    }
    
    /**
     * 将TaskMode映射到TemplateType
     */
    private static TemplateType mapTaskModeToTemplate(TaskMode taskMode) {
        return switch (taskMode) {
            case ARTICLE_SUMMARY -> TemplateType.ARTICLE_SUMMARY;
            case ARTICLE_EXCERPT -> TemplateType.ARTICLE_EXCERPT;
            case PUBLISH_CONGRATULATION -> TemplateType.PUBLISH_CONGRATULATION;
            case LOGIN_WELCOME -> TemplateType.LOGIN_WELCOME;
        };
    }
    
    @Override
    public String getName() {
        return advisorName;
    }
    
    @Override
    public int getOrder() {
        return order;
    }
    

    @Override
    public String buildPrompt() {
        log.debug("构建提示词，模板类型: {}, 变量: {}", templateType.getName(), variables);
        
        String prompt = buildFromTemplate(templateType, variables);
        
        if (isEmpty(prompt)) {
            log.debug("模板 {} 构建的提示词为空", templateType.getName());
            return null;
        }
        
        log.debug("提示词构建完成: {}", safeSubstring(prompt, 50));
        return prompt;
    }
    
    /**
     * 获取模板类型
     */
    public TemplateType getTemplateType() {
        return templateType;
    }
    
    /**
     * 获取模板变量
     */
    public Map<String, Object> getVariables() {
        return Map.copyOf(variables);
    }
}
