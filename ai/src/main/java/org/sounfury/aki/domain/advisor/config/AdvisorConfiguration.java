package org.sounfury.aki.domain.advisor.config;

import lombok.Builder;
import lombok.Data;
import org.sounfury.aki.domain.prompt.template.TemplateType;

/**
 * Advisor配置对象
 * 纯数据对象，包含所有Advisor相关的配置信息，完全技术无关
 */
@Data
@Builder
public class AdvisorConfiguration {
    
    /**
     * 角色ID
     */
    private String characterId;
    
    /**
     * 用户名称
     */
    private String userName;
    
    /**
     * 是否启用RAG功能
     */
    private boolean enableRag;
    
    /**
     * 是否启用记忆功能
     */
    private boolean enableMemory;
    
    /**
     * 行为指导模板类型
     */
    private TemplateType behaviorType;
    
    /**
     * RAG相关设置
     */
    private RagSettings ragSettings;
    
    /**
     * 记忆相关设置
     */
    private MemorySettings memorySettings;
    
    /**
     * 检查是否有有效的角色ID
     */
    public boolean hasCharacter() {
        return characterId != null && !characterId.trim().isEmpty();
    }
    
    /**
     * 检查是否有有效的用户名
     */
    public boolean hasUserName() {
        return userName != null && !userName.trim().isEmpty();
    }
    
    /**
     * 检查是否有有效的行为类型
     */
    public boolean hasBehaviorType() {
        return behaviorType != null;
    }
    
    /**
     * 检查RAG是否完全配置
     */
    public boolean isRagFullyConfigured() {
        return enableRag && ragSettings != null && ragSettings.isValid();
    }
    
    /**
     * 检查记忆是否完全配置
     */
    public boolean isMemoryFullyConfigured() {
        return enableMemory && memorySettings != null && memorySettings.isValid();
    }
}
