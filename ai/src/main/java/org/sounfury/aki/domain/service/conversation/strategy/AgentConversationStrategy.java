package org.sounfury.aki.domain.service.conversation.strategy;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.advisor.config.AdvisorConfiguration;
import org.sounfury.aki.domain.advisor.service.AdvisorConfigurationService;
import org.sounfury.aki.domain.llm.service.AdvisorAwareChatClientManager;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;

/**
 * Agent对话策略实现
 * 处理Agent对话，使用配置驱动的Advisor模式处理提示词组装
 * 启用记忆、角色卡和工具调用
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AgentConversationStrategy implements ConversationStrategy {

    private final AdvisorAwareChatClientManager chatClientManager;
    private final AdvisorConfigurationService configurationService;
    
    @Override
    public ConversationResult execute(ConversationRequest request) {
        LocalDateTime startTime = LocalDateTime.now();
        log.info("开始执行Agent对话，会话ID: {}", request.session().getSessionId().getValue());

        try {
            // 1. 构建Advisor配置（纯领域逻辑）
            AdvisorConfiguration config = configurationService.buildConfiguration(request);

            // 2. 获取配置驱动的ChatClient（自动包含工具和记忆）
            ChatClient chatClient = chatClientManager.createChatClient(config, request.session().getMode());

            // 3. 调用ChatClient，传递conversationId给MessageChatMemoryAdvisor
            String conversationId = request.session().getSessionId().getValue();
            ChatResponse chatResponse = chatClient
                    .prompt()
                    .user(request.userInput())
                    .advisors(a -> a.param(ChatMemory.CONVERSATION_ID, conversationId))
                    .call()
                    .chatResponse();

            String response = chatResponse.getResult().getOutput().getText();

            log.info("Agent对话执行完成，会话ID: {}, 耗时: {}ms",
                    request.session().getSessionId().getValue(),
                    java.time.Duration.between(startTime, LocalDateTime.now()).toMillis());

            return ConversationResult.success(response, getStrategyName(),
                    request.session().getSessionId().getValue());

        } catch (Exception e) {
            log.error("Agent对话执行失败，会话ID: {}",
                    request.session().getSessionId().getValue(), e);
            return ConversationResult.failure(e.getMessage(), getStrategyName(),
                    request.session().getSessionId().getValue());
        }
    }
    
    @Override
    public Flux<String> executeStream(ConversationRequest request) {
        log.info("开始执行流式Agent对话，会话ID: {}", request.session().getSessionId().getValue());

        try {
            // 1. 构建Advisor配置
            AdvisorConfiguration config = configurationService.buildConfiguration(request);

            // 2. 获取配置驱动的ChatClient（自动包含工具和记忆）
            ChatClient chatClient = chatClientManager.createChatClient(config, request.session().getMode());

            // 3. 调用流式接口，传递conversationId给MessageChatMemoryAdvisor
            String conversationId = request.session().getSessionId().getValue();
            return chatClient
                    .prompt()
                    .user(request.userInput())
                    .advisors(a -> a.param(ChatMemory.CONVERSATION_ID, conversationId))
                    .stream()
                    .content()
                    .doOnComplete(() -> {
                        log.info("流式Agent对话执行完成，会话ID: {}",
                                request.session().getSessionId().getValue());
                        // 注意：流式模式下需要在客户端收集完整响应后保存到记忆
                    })
                    .doOnError(error -> {
                        log.error("流式Agent对话执行失败，会话ID: {}",
                                request.session().getSessionId().getValue(), error);
                    });

        } catch (Exception e) {
            log.error("流式Agent对话执行异常，会话ID: {}",
                    request.session().getSessionId().getValue(), e);
            return Flux.error(e);
        }
    }
    
    @Override
    public String getStrategyName() {
        return "agent-conversation-strategy";
    }
    
}

