package org.sounfury.aki.domain.common;

/**
 * 领域层Advisor抽象接口
 * 定义Advisor的基本契约，保持技术无关性
 */
public interface DomainAdvisor {

    /**
     * 获取Advisor名称
     * @return Advisor名称
     */
    String getName();

    /**
     * 获取执行顺序
     * 数值越小，优先级越高，越先执行
     * @return 执行顺序
     */
    int getOrder();

    /**
     * 构建提示词内容
     * 这是Advisor的核心功能，构建相应的提示词
     * @return 构建的提示词内容，如果返回null或空字符串则跳过此Advisor
     */
    String buildPrompt();
}
