package org.sounfury.aki.domain.character;

import java.util.List;
import java.util.Optional;

/**
 * 角色仓储接口
 * 定义角色聚合的持久化操作
 */
public interface CharacterRepository {
    
    /**
     * 根据ID查找角色
     * @param id 角色ID
     * @return 角色实体，如果不存在则返回空
     */
    Optional<Character> findById(CharacterId id);
    
    /**
     * 根据名称查找角色
     * @param name 角色名称
     * @return 角色实体，如果不存在则返回空
     */
    Optional<Character> findByName(String name);
    
    /**
     * 查找所有启用的角色
     * @return 启用的角色列表
     */
    List<Character> findAllEnabled();
    
    /**
     * 查找所有角色
     * @return 所有角色列表
     */
    List<Character> findAll();
    
    /**
     * 保存角色
     * @param character 角色实体
     * @return 保存后的角色实体
     */
    Character save(Character character);
    
    /**
     * 删除角色
     * @param id 角色ID
     */
    void deleteById(CharacterId id);
    
    /**
     * 检查角色是否存在
     * @param id 角色ID
     * @return 如果存在返回true，否则返回false
     */
    boolean existsById(CharacterId id);
    
    /**
     * 检查角色名称是否存在
     * @param name 角色名称
     * @return 如果存在返回true，否则返回false
     */
    boolean existsByName(String name);
}
