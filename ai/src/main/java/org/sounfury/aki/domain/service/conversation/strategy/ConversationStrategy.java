package org.sounfury.aki.domain.service.conversation.strategy;

import org.sounfury.aki.domain.chatsession.ChatSession;
import reactor.core.publisher.Flux;

/**
 * 对话策略接口
 * 定义有状态对话的处理策略
 */
public interface ConversationStrategy {
    
    /**
     * 执行对话
     * @param request 对话请求
     * @return 对话结果
     */
    ConversationResult execute(ConversationRequest request);
    
    /**
     * 执行流式对话
     * @param request 对话请求
     * @return 流式对话结果
     */
    Flux<String> executeStream(ConversationRequest request);
    
    /**
     * 获取策略名称
     * @return 策略名称
     */
    String getStrategyName();
    
    /**
     * 对话请求
     */
    record ConversationRequest(
            String userInput,
            String userName,
            boolean userLoggedIn,
            ChatSession session
    ) {}
    
    /**
     * 对话结果
     */
    record ConversationResult(
            String response,
            boolean success,
            String errorMessage,
            String strategyName,
            String sessionId
    ) {
        public static ConversationResult success(String response, String strategyName, String sessionId) {
            return new ConversationResult(response, true, null, strategyName, sessionId);
        }
        
        public static ConversationResult failure(String errorMessage, String strategyName, String sessionId) {
            return new ConversationResult(null, false, errorMessage, strategyName, sessionId);
        }
    }
}
