package org.sounfury.aki.domain.prompt.template;

import lombok.Builder;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.Set;
import java.util.HashSet;

/**
 * 提示词模板聚合根
 * 管理提示词模板的完整生命周期和业务规则
 */
@Getter
@Builder
public class PromptTemplate {
    
    /**
     * 模板唯一标识
     */
    private final TemplateId id;
    
    /**
     * 模板类型
     */
    private final TemplateType type;
    
    /**
     * 模板名称
     */
    private final String name;
    
    /**
     * 模板内容，支持占位符 {placeholder}
     */
    private final String template;
    
    /**
     * 占位符说明
     */
    private final Map<String, String> placeholders;
    
    /**
     * 是否启用
     */
    private final boolean enabled;
    
    /**
     * 创建时间
     */
    private final LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private final LocalDateTime updatedAt;
    
    /**
     * 模板描述
     */
    private final String description;
    
    /**
     * 占位符正则表达式
     */
    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\{([^}]+)\\}");
    
    /**
     * 创建新模板
     */
    public static PromptTemplate create(TemplateId id, TemplateType type, String name, 
                                       String template, Map<String, String> placeholders, String description) {
        if (id == null) {
            throw new IllegalArgumentException("模板ID不能为空");
        }
        if (type == null) {
            throw new IllegalArgumentException("模板类型不能为空");
        }
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("模板名称不能为空");
        }
        if (template == null || template.trim().isEmpty()) {
            throw new IllegalArgumentException("模板内容不能为空");
        }
        
        LocalDateTime now = LocalDateTime.now();
        return PromptTemplate.builder()
                .id(id)
                .type(type)
                .name(name.trim())
                .template(template.trim())
                .placeholders(placeholders)
                .description(description)
                .enabled(true)
                .createdAt(now)
                .updatedAt(now)
                .build();
    }
    
    /**
     * 更新模板内容
     */
    public PromptTemplate updateTemplate(String newTemplate, Map<String, String> newPlaceholders) {
        if (newTemplate == null || newTemplate.trim().isEmpty()) {
            throw new IllegalArgumentException("新模板内容不能为空");
        }
        
        return PromptTemplate.builder()
                .id(this.id)
                .type(this.type)
                .name(this.name)
                .template(newTemplate.trim())
                .placeholders(newPlaceholders)
                .description(this.description)
                .enabled(this.enabled)
                .createdAt(this.createdAt)
                .updatedAt(LocalDateTime.now())
                .build();
    }
    
    /**
     * 启用模板
     */
    public PromptTemplate enable() {
        if (this.enabled) {
            return this;
        }
        
        return PromptTemplate.builder()
                .id(this.id)
                .type(this.type)
                .name(this.name)
                .template(this.template)
                .placeholders(this.placeholders)
                .description(this.description)
                .enabled(true)
                .createdAt(this.createdAt)
                .updatedAt(LocalDateTime.now())
                .build();
    }
    
    /**
     * 禁用模板
     */
    public PromptTemplate disable() {
        if (!this.enabled) {
            return this;
        }
        
        return PromptTemplate.builder()
                .id(this.id)
                .type(this.type)
                .name(this.name)
                .template(this.template)
                .placeholders(this.placeholders)
                .description(this.description)
                .enabled(false)
                .createdAt(this.createdAt)
                .updatedAt(LocalDateTime.now())
                .build();
    }
    
    /**
     * 渲染模板，替换占位符
     */
    public String render(Map<String, String> variables) {
        if (variables == null || variables.isEmpty()) {
            return template;
        }
        
        String result = template;
        for (Map.Entry<String, String> entry : variables.entrySet()) {
            String placeholder = "{" + entry.getKey() + "}";
            String value = entry.getValue() != null ? entry.getValue() : "";
            result = result.replace(placeholder, value);
        }
        
        return result;
    }
    
    /**
     * 提取模板中的所有占位符
     */
    public Set<String> extractPlaceholders() {
        Set<String> placeholders = new HashSet<>();
        Matcher matcher = PLACEHOLDER_PATTERN.matcher(template);
        while (matcher.find()) {
            placeholders.add(matcher.group(1));
        }
        return placeholders;
    }
    
    /**
     * 检查模板是否有效
     */
    public boolean isValid() {
        return template != null && !template.trim().isEmpty() && enabled;
    }
}
