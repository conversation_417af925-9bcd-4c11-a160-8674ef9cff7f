package org.sounfury.aki.domain.service.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.service.task.enums.TaskMode;
import org.sounfury.aki.domain.service.task.strategy.ArticleTaskStrategy;
import org.sounfury.aki.domain.service.task.strategy.CompanionTaskStrategy;
import org.sounfury.aki.domain.service.task.strategy.TaskStrategy;
import org.springframework.stereotype.Component;

/**
 * 任务编排工厂
 * 根据任务模式返回对应的策略实例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskOrchestrationFactory {
    
    private final ArticleTaskStrategy articleTaskStrategy;
    private final CompanionTaskStrategy companionTaskStrategy;
    
    /**
     * 根据任务模式获取对应的策略
     * @param taskMode 任务模式
     * @return 对应的策略实例
     */
    public TaskStrategy getStrategy(TaskMode taskMode) {
        log.debug("获取任务策略，任务模式: {}", taskMode);
        
        if (taskMode.isArticleTask()) {
            return articleTaskStrategy;
        } else if (taskMode.isCompanionTask()) {
            return companionTaskStrategy;
        } else {
            throw new IllegalArgumentException("不支持的任务模式: " + taskMode);
        }
    }
}
