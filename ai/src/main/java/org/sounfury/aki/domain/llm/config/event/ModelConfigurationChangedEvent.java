package org.sounfury.aki.domain.llm.config.event;

import lombok.Builder;
import lombok.Getter;
import org.sounfury.aki.domain.llm.config.LlmConfiguration;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 模型配置变更领域事件
 * 当LLM配置发生变更时发布此事件
 */
@Getter
public class ModelConfigurationChangedEvent extends ApplicationEvent {
    
    /**
     * 配置ID
     */
    private final String configurationId;
    
    /**
     * 变更前的配置
     */
    private final LlmConfiguration oldConfiguration;
    
    /**
     * 变更后的配置
     */
    private final LlmConfiguration newConfiguration;
    
    /**
     * 变更时间
     */
    private final LocalDateTime changeTime;
    
    /**
     * 变更类型
     */
    private final ChangeType changeType;
    
    /**
     * 变更描述
     */
    private final String changeDescription;
    
    @Builder
    public ModelConfigurationChangedEvent(
            String configurationId,
            LlmConfiguration oldConfiguration,
            LlmConfiguration newConfiguration,
            LocalDateTime changeTime) {
        
        super(newConfiguration);
        this.configurationId = configurationId;
        this.oldConfiguration = oldConfiguration;
        this.newConfiguration = newConfiguration;
        this.changeTime = changeTime != null ? changeTime : LocalDateTime.now();
        this.changeType = determineChangeType(oldConfiguration, newConfiguration);
        this.changeDescription = generateChangeDescription(oldConfiguration, newConfiguration);
    }
    
    /**
     * 变更类型枚举
     */
    public enum ChangeType {
        PROVIDER_CHANGED("模型提供商变更"),
        SETTINGS_CHANGED("模型参数变更"),
        ENABLED_CHANGED("启用状态变更"),
        FULL_UPDATE("完整配置更新"),
        INITIALIZATION("初始化配置");
        
        private final String description;
        
        ChangeType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 确定变更类型
     */
    private ChangeType determineChangeType(LlmConfiguration oldConfig, LlmConfiguration newConfig) {
        if (oldConfig == null) {
            return ChangeType.INITIALIZATION;
        }
        
        boolean providerChanged = !oldConfig.getProvider().equals(newConfig.getProvider());
        boolean settingsChanged = !oldConfig.getSettings().equals(newConfig.getSettings());
        boolean enabledChanged = oldConfig.isEnabled() != newConfig.isEnabled();
        
        if (providerChanged && settingsChanged) {
            return ChangeType.FULL_UPDATE;
        } else if (providerChanged) {
            return ChangeType.PROVIDER_CHANGED;
        } else if (settingsChanged) {
            return ChangeType.SETTINGS_CHANGED;
        } else if (enabledChanged) {
            return ChangeType.ENABLED_CHANGED;
        } else {
            return ChangeType.FULL_UPDATE;
        }
    }
    
    /**
     * 生成变更描述
     */
    private String generateChangeDescription(LlmConfiguration oldConfig, LlmConfiguration newConfig) {
        if (oldConfig == null) {
            return String.format("初始化LLM配置: %s - %s", 
                    newConfig.getProvider().getDisplayName(), 
                    newConfig.getProvider().getModelName());
        }
        
        StringBuilder description = new StringBuilder();
        
        // 检查提供商变更
        if (!oldConfig.getProvider().equals(newConfig.getProvider())) {
            description.append(String.format("提供商从 %s(%s) 变更为 %s(%s); ",
                    oldConfig.getProvider().getDisplayName(),
                    oldConfig.getProvider().getModelName(),
                    newConfig.getProvider().getDisplayName(),
                    newConfig.getProvider().getModelName()));
        }
        
        // 检查关键参数变更
        if (!oldConfig.getSettings().equals(newConfig.getSettings())) {
            if (!oldConfig.getSettings().getMaxTokens().equals(newConfig.getSettings().getMaxTokens())) {
                description.append(String.format("最大Token从 %d 变更为 %d; ",
                        oldConfig.getSettings().getMaxTokens(),
                        newConfig.getSettings().getMaxTokens()));
            }
            
            if (!oldConfig.getSettings().getTemperature().equals(newConfig.getSettings().getTemperature())) {
                description.append(String.format("温度从 %.2f 变更为 %.2f; ",
                        oldConfig.getSettings().getTemperature(),
                        newConfig.getSettings().getTemperature()));
            }
        }
        
        // 检查启用状态变更
        if (oldConfig.isEnabled() != newConfig.isEnabled()) {
            description.append(String.format("启用状态从 %s 变更为 %s; ",
                    oldConfig.isEnabled() ? "启用" : "禁用",
                    newConfig.isEnabled() ? "启用" : "禁用"));
        }
        
        return description.length() > 0 ? description.toString() : "配置已更新";
    }
    
    /**
     * 检查是否为关键变更（需要重新初始化模型）
     */
    public boolean isCriticalChange() {
        return changeType == ChangeType.PROVIDER_CHANGED || 
               changeType == ChangeType.FULL_UPDATE ||
               changeType == ChangeType.INITIALIZATION ||
               (changeType == ChangeType.ENABLED_CHANGED && newConfiguration.isEnabled());
    }
    
    /**
     * 检查是否为参数调整（不需要重新初始化模型）
     */
    public boolean isParameterAdjustment() {
        return changeType == ChangeType.SETTINGS_CHANGED;
    }
    
    /**
     * 获取事件摘要
     */
    public String getEventSummary() {
        return String.format("[%s] %s - %s", 
                changeType.getDescription(), 
                configurationId, 
                changeDescription);
    }
    
    @Override
    public String toString() {
        return "ModelConfigurationChangedEvent{" +
               "configurationId='" + configurationId + '\'' +
               ", changeType=" + changeType +
               ", changeTime=" + changeTime +
               ", changeDescription='" + changeDescription + '\'' +
               '}';
    }
}
