server:
  port: 8090    # 服务端口，默认8080，可自定义

spring:
  application:
    name: aki
  ai:
    chat:
      memory:
        repository:
          jdbc:
            initialize-schema: always
    openai:
      chat:
        options:
          model: deepseek-ai/DeepSeek-V3
      base-url: https://api.siliconflow.cn/v1
      api-key: sk-nvvdrwdosspjsmvqwyzpydppglryorujwzynmxilfqumfqad
##数据库
  datasource:
    url: ***************************************************************************************************************************
    username: root
    password: a2133266
    driver-class-name: com.mysql.cj.jdbc.Driver
# 日志级别（可选）
logging:
  level:
    root: debug
