# AI流式输出支持

## 任务描述
修改AI对话和总结API，支持流式输出，同时保持非流式输出的兼容性。前端通过不同的API端点选择流式或非流式模式。

## 实施方案
使用Spring AI原生的`stream()`方法 + Flux响应，利用reactor-core实现响应式数据流处理。

## 修改内容

### 1. 编排策略接口层
**文件：** `ai/src/main/java/org/sounfury/aki/orchestration/strategy/OrchestrationStrategy.java`
- ✅ 添加`Flux<String> executeStream(OrchestrationContext context)`接口方法
- ✅ 保持现有`execute()`方法用于非流式

### 2. 对话策略实现
**文件：** `ai/src/main/java/org/sounfury/aki/orchestration/strategy/ConversationStrategy.java`
- ✅ 添加`executeStream()`方法实现
- ✅ 使用`chatModel.stream(prompt)`替代`call(prompt)`
- ✅ 返回`Flux<String>`流式响应
- ✅ 添加错误处理和日志记录

### 3. 总结策略实现
**文件：** `ai/src/main/java/org/sounfury/aki/orchestration/strategy/SummarizationStrategy.java`
- ✅ 添加`executeStream()`方法实现
- ✅ 使用`chatModel.stream(prompt)`替代`call(prompt)`
- ✅ 返回`Flux<String>`流式响应
- ✅ 添加错误处理和日志记录

### 4. 编排器层
**文件：** `ai/src/main/java/org/sounfury/aki/orchestration/AiOrchestrator.java`
- ✅ 添加`orchestrateStream()`方法
- ✅ 保持现有`orchestrate()`方法用于非流式
- ✅ 添加流式编排的错误处理

### 5. 对话控制器
**文件：** `ai/src/main/java/org/sounfury/aki/api/ChatController.java`
- ✅ 添加流式对话接口`/ai/chat/talk/stream`
- ✅ 保持现有非流式接口`/ai/chat/talk`
- ✅ 设置`MediaType.TEXT_EVENT_STREAM_VALUE`
- ✅ 添加流式响应的错误处理

### 6. 总结控制器
**文件：** `ai/src/main/java/org/sounfury/aki/api/ActionController.java`
- ✅ 添加流式总结接口`/ai/action/summarize/article/{articleId}/stream`
- ✅ 保持现有非流式接口`/ai/action/summarize/article/{articleId}`
- ✅ 设置`MediaType.TEXT_EVENT_STREAM_VALUE`
- ✅ 添加流式响应的错误处理

## API接口设计

### 对话接口
- **非流式：** `GET /ai/chat/talk` - 返回JSON格式完整响应
- **流式：** `GET /ai/chat/talk/stream` - 返回`text/event-stream`格式流式响应

### 总结接口
- **非流式：** `POST /ai/action/summarize/article/{id}` - 返回JSON格式完整响应
- **流式：** `GET /ai/action/summarize/article/{id}/stream` - 返回`text/event-stream`格式流式响应

## 技术实现要点

1. **依赖支持：** 使用已有的`reactor-core`依赖，无需额外引入
2. **流式处理：** 使用`chatModel.stream(prompt)`获取`Flux<ChatResponse>`
3. **数据转换：** 将`ChatResponse`转换为`String`内容
4. **错误处理：** 在每个层级添加适当的错误处理和日志记录
5. **向后兼容：** 保持所有现有API不变，新增流式API作为补充

## 前端使用方式

### 流式接口（EventSource）
```javascript
const eventSource = new EventSource('/ai/chat/talk/stream?message=hello&sessionId=123');
eventSource.onmessage = function(event) {
    console.log('收到数据:', event.data);
};
```

### 非流式接口（传统AJAX）
```javascript
fetch('/ai/chat/talk?message=hello&sessionId=123')
    .then(response => response.json())
    .then(data => console.log('完整响应:', data));
```

## 状态
✅ 已完成所有核心功能实现
✅ 已完成DDD改造第一阶段（领域模型和仓储）
⏳ 待测试验证
