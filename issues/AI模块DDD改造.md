# AI模块DDD改造任务

## 背景
将AI模块从传统分层架构改造为DDD（领域驱动设计）架构，引入聚合根、值对象、仓储模式等DDD概念，同时建立模板化的提示词拼接系统。

## 改造方案
采用渐进式DDD改造，保持现有架构基础，逐步引入DDD概念。

## 执行计划

### 第一阶段：领域模型和仓储接口 ✅
- ✅ CharacterId - 角色ID值对象
- ✅ Character - 角色聚合根
- ✅ CharacterCard - 角色卡值对象（重构）
- ✅ CharacterRepository - 角色仓储接口
- ✅ TemplateId - 模板ID值对象
- ✅ TemplateType - 模板类型枚举
- ✅ PromptTemplate - 提示词模板聚合根
- ✅ PromptTemplateRepository - 提示词模板仓储接口

### 第二阶段：基础设施层实现 ✅
- ✅ InMemoryCharacterRepository - 内存版角色仓储
- ✅ CharacterData - 角色数据初始化类
- ✅ InMemoryPromptTemplateRepository - 内存版提示词模板仓储
- ✅ PromptTemplateData - 提示词模板数据初始化类

### 第三阶段：领域服务层 ✅
- ✅ PromptAssemblyService - 提示词组装领域服务

### 第四阶段：应用服务层重构 ✅
- ✅ 重构DefaultCharacterProvider使用新仓储
- ✅ 重构PromptBuilderFactory使用新领域服务
- ✅ 更新SummarizationStrategy使用新架构
- ✅ 清理不再需要的文件（CharacterCardService、模板类等）

### 第五阶段：测试验证 ⏳
- ⏳ 功能测试验证
- ⏳ 性能测试验证

## 核心改进

### 1. 领域模型设计
```java
// 角色聚合根
Character {
    CharacterId id;           // 值对象ID
    String name;              // 角色名称
    CharacterCard card;       // 角色卡值对象
    boolean enabled;          // 是否启用
    LocalDateTime createdAt;  // 创建时间
}

// 提示词模板聚合根
PromptTemplate {
    TemplateId id;                    // 值对象ID
    TemplateType type;                // 模板类型枚举
    String template;                  // 模板内容
    Map<String, String> placeholders; // 占位符说明
    boolean enabled;                  // 是否启用
}
```

### 2. 模板化提示词系统
支持以下模板类型：
- **SYSTEM_PROMPT** - 系统提示词："你是世界一流的演员"
- **PERSONALITY_PROMPT** - 性格描述："你的性格是{personality}"
- **SCENARIO_PROMPT** - 场景设定："当前场景是{scenario}"
- **EXAMPLE_DIALOGUE** - 示例对话："示例对话为{examples}"
- **CHARACTER_INTRO** - 角色介绍："你扮演的角色是{character_name}"
- **USER_ADDRESS** - 用户称呼："用户名字是{user_name}"
- **BEHAVIOR_GUIDE** - 行为指导："请保持角色一致性"

### 3. 仓储模式
- **接口定义**：CharacterRepository、PromptTemplateRepository
- **内存实现**：InMemoryCharacterRepository、InMemoryPromptTemplateRepository
- **数据初始化**：CharacterData、PromptTemplateData
- **后续扩展**：可轻松切换到数据库实现

### 4. 领域服务
- **PromptAssemblyService**：负责将模板和角色信息组装成完整提示词
- **模板渲染**：支持占位符替换，如{personality}、{scenario}等
- **组装策略**：系统提示词 + 角色介绍 + 性格描述 + 场景设定 + 用户称呼 + 示例对话 + 行为指导

## 技术优势

### 1. DDD概念引入
- **聚合根**：Character、PromptTemplate管理业务不变性
- **值对象**：CharacterId、TemplateId、CharacterCard保证不可变性
- **仓储模式**：抽象数据访问，便于测试和切换存储
- **领域服务**：封装复杂业务逻辑

### 2. 模板化设计
- **数据驱动**：提示词模板可通过数据配置
- **占位符支持**：灵活的变量替换机制
- **类型安全**：TemplateType枚举确保类型正确性
- **易于扩展**：新增模板类型只需扩展枚举

### 3. 架构优势
- **职责清晰**：领域逻辑集中在领域服务
- **易于测试**：仓储接口便于Mock测试
- **易于扩展**：新增角色或模板无需修改核心逻辑
- **数据源无关**：可轻松从内存切换到数据库

## 迁移策略

### 1. 数据迁移
- 现有硬编码角色数据迁移到CharacterData
- 提示词拼接逻辑迁移到PromptAssemblyService
- 保持API兼容性，内部实现切换

### 2. 渐进式改造
- 第一步：建立新的领域模型和仓储
- 第二步：重构现有Provider使用新仓储
- 第三步：更新提示词拼接逻辑
- 第四步：测试验证功能正常

### 3. 后续扩展
- 数据库持久化：实现JPA版本的仓储
- 缓存支持：在仓储层添加缓存机制
- 配置管理：支持动态配置模板和角色
- 版本控制：支持模板版本管理

## 第四阶段完成总结

### 重构内容
1. **DefaultCharacterProvider** - 从CharacterCardService迁移到CharacterRepository
2. **PromptBuilderFactory** - 集成PromptAssemblyService，支持新旧两种模式
3. **SummarizationStrategy** - 使用总结专家角色和新的提示词组装服务
4. **文件清理** - 删除CharacterCardService、PersonSystemPromptTemplate、ActionSystemPromptTemplate

### 兼容性保证
- 保持API接口不变，内部实现切换到DDD架构
- 提供兼容模式，确保在找不到角色时能正常工作
- 保留CharacterCard类，作为值对象继续使用

### 技术改进
- 模板化提示词拼接，支持占位符替换
- 角色数据驱动，便于扩展和维护
- 领域逻辑集中，职责更加清晰
- 仓储模式抽象，便于后续切换数据源

## 第五阶段完成总结 - 编排策略优化

### 策略重新设计
1. **ConversationStrategy** - 普通对话，开启记忆、角色卡，不开启工具
2. **ActionStrategy** - 特定任务执行，挂载角色卡、动作特定模板，不开启记忆和工具
3. **AgentStrategy** - 智能代理，开启记忆、角色卡、工具调用
4. **~~SummarizationStrategy~~** - 已删除，完全使用ActionStrategy替代

### 动态行为指导系统
- 扩展TemplateType：CONVERSATION_BEHAVIOR、ACTION_BEHAVIOR、AGENT_BEHAVIOR
- PromptAssemblyService支持动态选择行为指导模板
- 根据策略类型注入相应的行为提示词

### 动态用户称呼系统
- 已登录用户：sounfury
- 未登录用户：旅行者
- OrchestrationContext支持userLoggedIn字段
- 各策略自动根据登录状态调整用户称呼

### 策略工厂优化
- OrchestrationStrategy接口增加supports()和getPriority()方法
- 支持策略优先级排序和自动选择
- 移除SUMMARIZATION类型，统一使用ACTION

## 第六阶段完成总结 - ActionStrategy优化

### 删除SummarizationStrategy
- 完全删除SummarizationStrategy类
- 统一使用ActionStrategy处理所有特定任务

### 动作特定模板系统
- 新增ACTION_SUMMARY、ACTION_TRANSLATION、ACTION_CODE_GENERATION模板类型
- PromptAssemblyService支持根据动作名称自动选择对应模板
- 支持动作名称映射：summary/summarize/总结 → ACTION_SUMMARY

### 动作名称支持
- OrchestrationContext新增actionName字段
- AiOrchestrator.Builder支持设置动作名称
- ActionStrategy根据动作名称动态注入特定模板

### API更新
- ActionController使用actionName("summary")指定总结动作
- RequestType统一使用ACTION，移除SUMMARIZATION
- 保持API接口不变，内部实现优化

### 清理工作
- 删除SummarizerCharacterProvider类
- 更新ActionController使用DefaultCharacterProvider
- 删除buildSummarizePrompt方法，使用ACTION_SUMMARY模板

## 第七阶段完成总结 - 统一执行器重构

### 架构大幅简化
- **删除所有策略类**：ConversationStrategy、ActionStrategy、AgentStrategy、OrchestrationStrategy
- **删除策略工厂**：OrchestrationStrategyFactory
- **统一执行器**：UnifiedOrchestrationExecutor处理所有场景

### 模式化配置
- **OrchestrationMode枚举**：CONVERSATION、ACTION、AGENT三种模式
- **Builder模式方法**：conversationMode()、actionMode()、agentMode()
- **自动配置**：根据模式自动设置记忆、工具、行为指导等

### 代码量大幅减少
- **从5个策略类** → **1个统一执行器**
- **从复杂的策略工厂** → **简单的模式配置**
- **从重复的构建逻辑** → **统一的构建方法**

### 使用方式简化
```java
// 对话模式
AiOrchestrator.builder()
    .conversationMode()
    .buildContext();

// 动作模式
AiOrchestrator.builder()
    .actionMode("summary")
    .buildContext();

// Agent模式
AiOrchestrator.builder()
    .agentMode()
    .buildContext();
```

## 第八阶段完成总结 - 删除RequestType

### 彻底清理冗余概念
- **删除RequestType枚举**：完全移除RequestType.java文件
- **统一使用OrchestrationMode**：只保留一个模式概念
- **简化Builder**：移除requestType()方法，直接使用模式方法

### 概念更清晰
- **之前**：RequestType + OrchestrationMode 两个概念重复
- **现在**：只有OrchestrationMode 一个概念
- **更直观**：模式即配置，一目了然

### 代码更简洁
- 减少了重复的概念和转换逻辑
- Builder方法更直接，不需要额外的类型设置
- 消除了RequestType到OrchestrationMode的映射代码

## 当前状态
✅ 第一阶段：领域模型和仓储接口已完成
✅ 第二阶段：基础设施层实现已完成
✅ 第三阶段：领域服务层已完成
✅ 第四阶段：应用服务层重构已完成
✅ 第五阶段：编排策略优化已完成
✅ 第六阶段：ActionStrategy优化已完成
✅ 第七阶段：统一执行器重构已完成
✅ 第八阶段：删除RequestType已完成
⏳ 第九阶段：测试验证待开始
