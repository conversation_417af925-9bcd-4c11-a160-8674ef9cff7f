# LLM动态切换优化任务

## 任务背景
用户要求将LLM动态切换从DeepSeek特定接口统一换成OpenAI规范接口，简化代码结构并提高可维护性。

## 执行计划
### 第一阶段：清理和修复 ✅
1. 修复ConfigurableChatModelFactory.java语法错误
2. 移除DeepSeek特定依赖和导入
3. 清理application.yml中的DeepSeek配置

### 第二阶段：实现统一OpenAI接口 ✅
1. 重构ConfigurableChatModelFactory
2. 实现统一的OpenAI ChatModel创建方法
3. 移除所有特定提供商的创建方法
4. 创建ModelSettings到OpenAI ChatOptions的映射方法

### 第三阶段：优化和测试 ✅
1. 更新默认配置使用OpenAI接口
2. 清理相关引用

## 主要变更

### 1. ConfigurableChatModelFactory.java
- 移除DeepSeek特定导入，添加OpenAI导入
- 统一使用`createUnifiedChatModel`方法
- 实现`buildChatOptions`方法进行参数映射
- 移除所有特定提供商的创建方法

### 2. UnifiedOrchestrationExecutor.java
- 移除DeepSeek特定导入

### 3. application.yml
- 将`spring.ai.deepseek`改为`spring.ai.openai`

### 4. LlmConfiguration.java
- 更新`createDefault()`方法使用统一的`ModelProvider.create`

### 5. ModelProvider.java
- 移除所有特定提供商的创建方法（createDeepSeek、createOpenAI等）
- 统一为`create()`方法，支持所有提供商类型
- 添加`getDefaultBaseUrl()`方法提供默认URL配置

### 6. LlmConfigurationApplicationService.java
- 重构`buildModelProvider()`方法使用统一的创建方式

## 技术优势
1. **统一接口**：所有LLM提供商通过OpenAI规范接口访问
2. **简化代码**：移除了大量特定提供商的重复代码
3. **易于维护**：统一的参数映射和配置管理
4. **更好扩展性**：新增提供商只需配置baseUrl和模型名

## 兼容性
- 保持现有功能不变
- 支持所有原有的LLM提供商（DeepSeek、OpenAI、Claude等）
- 通过baseUrl区分不同提供商

## 测试建议
1. 测试默认配置的模型创建
2. 测试不同提供商的动态切换
3. 验证参数映射的正确性
4. 确认缓存机制正常工作
