# ChatClient解耦重构任务

## 任务背景
用户指出buildOptions方法可以删除，因为LLM配置已经统一管理。同时ChatClient/ChatModel应该通过依赖注入来解耦，而不是在执行器中直接创建。

## 执行计划
### 第一阶段：删除重复配置 ✅
1. 删除OrchestrationContext中的温度和maxTokens字段
2. 删除AiOrchestrator.Builder中的相关字段和方法
3. 删除buildOptions方法和DeepSeekChatOptions相关代码

### 第二阶段：创建ChatClientProvider服务 ✅
1. 创建ChatClientProvider接口
2. 实现DynamicChatClientProvider服务
3. 支持根据模式和工具启用状态提供合适的ChatClient

### 第三阶段：重构UnifiedOrchestrationExecutor ✅
1. 移除chatModelFactory和getDynamicChatModel方法
2. 注入ChatClientProvider服务
3. 简化Prompt创建逻辑
4. 统一ChatClient的获取和使用方式

## 主要变更

### 1. OrchestrationContext.java
- 删除maxTokens和temperature字段
- 保持模块启用状态管理（记忆、工具、世界书）

### 2. AiOrchestrator.java
- 删除Builder中的maxTokens和temperature字段
- 保持模式配置方法（conversationMode、actionMode、agentMode）

### 3. ChatClientProvider接口
- 定义根据编排上下文获取ChatClient的契约
- 支持工具列表参数，用于AGENT模式

### 4. DynamicChatClientProvider实现
- 基于LLM配置动态提供ChatClient实例
- 根据模式和工具启用状态决定是否添加工具
- 集成ConfigurableChatModelFactory获取ChatModel

### 5. UnifiedOrchestrationExecutor.java
- 移除buildOptions和getDynamicChatModel方法
- 注入ChatClientProvider替代chatModelFactory
- 简化Prompt创建，不再传递ChatOptions
- 统一ChatClient获取逻辑

## 技术优势
1. **配置统一管理**：温度、maxTokens等参数由LLM配置统一管理
2. **依赖解耦**：通过ChatClientProvider实现依赖注入
3. **职责清晰**：执行器专注编排逻辑，不再关心ChatModel创建
4. **代码简化**：移除重复的ChatClient创建逻辑

## 架构改进
- **单一职责**：OrchestrationContext专注模块启用状态管理
- **依赖倒置**：UnifiedOrchestrationExecutor依赖抽象接口
- **配置集中**：所有LLM相关配置统一管理
- **模式驱动**：保持原有的模式化配置逻辑

## 兼容性
- 保持现有的模式配置API不变
- 保持模块启用逻辑不变
- 保持编排执行结果不变

重构完成，实现了真正的解耦和配置统一管理！
