# Orchestration激进重构任务

## 背景
DDD项目中orchestration错误地放在了领域层，应该移到应用层。同时使用了过多设计模式导致代码臃肿，需要简化为工厂+策略模式。

## 功能边界
- 保留：三种编排模式、角色卡/记忆/工具配置、API兼容性
- 重构：orchestration移到应用层，简化为工厂+策略模式
- 不涉及：新功能、业务逻辑变更、API接口变更

## 执行计划

### 第一阶段：Session聚合根抽离 ⏳
**目标**：将session相关逻辑从orchestration抽离到chatsession聚合根

#### 1.1 创建ChatSession聚合根
- [ ] SessionId - 会话ID值对象（从orchestration移动）
- [ ] ChatSession - 会话聚合根（整合ConversationContext逻辑）
- [ ] SessionRepository - 会话仓储接口
- [ ] SessionDomainService - 会话领域服务

#### 1.2 移动现有Session相关类
- [ ] domain/orchestration/model/SessionId → domain/chatsession/SessionId
- [ ] domain/orchestration/model/ConversationContext → domain/chatsession/ChatSession
- [ ] domain/orchestration/ConversationDomainService → domain/chatsession/SessionDomainService
- [ ] infrastructure/orchestration/InMemoryConversationService → infrastructure/chatsession/InMemorySessionRepository

### 第二阶段：删除基础设施层orchestration ⏳
**目标**：完全删除infrastructure/orchestration下的所有实现

#### 2.1 删除文件清单
- [ ] infrastructure/orchestration/ 整个目录及其所有内容

#### 2.2 删除领域层orchestration
- [ ] domain/orchestration/ 整个目录及其所有内容

### 第三阶段：应用层重构 ⏳
**目标**：按照conversation和task分包重构

#### 3.1 Conversation包重构（涉及session、记忆、工具）
- [ ] application/conversation/ConversationOrchestrationService - 对话编排服务
- [ ] application/conversation/AgentOrchestrationService - Agent编排服务
- [ ] ConversationOrchestrationFactory - 对话编排工厂
- [ ] ConversationStrategy - 对话策略接口
- [ ] ChatConversationStrategy - 聊天对话策略
- [ ] AgentConversationStrategy - Agent对话策略

#### 3.2 Task包重构（只涉及角色卡，无session）
- [ ] application/task/TaskOrchestrationService - 任务编排服务
- [ ] TaskOrchestrationFactory - 任务编排工厂
- [ ] TaskStrategy - 任务策略接口
- [ ] ActionTaskStrategy - 动作任务策略

#### 3.3 重构现有应用服务
- [ ] ChatApplicationService → 使用conversation包的服务
- [ ] AgentApplicationService → 使用conversation包的服务
- [ ] ActionApplicationService → 使用task包的服务

### 第四阶段：简化设计模式 ⏳
**目标**：移除过度抽象，只保留工厂+策略模式

#### 4.1 删除过度抽象
- [ ] 删除所有Provider接口和实现
- [ ] 删除OrchestrationComponent抽象
- [ ] 删除复杂的Builder模式

#### 4.2 直接依赖注入
- [ ] 应用服务直接注入具体实现类
- [ ] 使用Spring的@Autowired简化依赖管理

### 第五阶段：配置和测试清理 ⏳
**目标**：清理配置类和更新测试

#### 5.1 配置类简化
- [ ] 删除OrchestrationConfiguration
- [ ] 在AppConfig中添加必要的Bean配置

#### 5.2 测试更新
- [ ] 更新所有相关测试类
- [ ] 验证功能正常

## 最终架构
```
domain/chatsession/ (独立的Session聚合根)
├── ChatSession
├── SessionId  
├── SessionRepository
└── SessionDomainService

application/conversation/ (涉及session、记忆、工具)
├── ConversationOrchestrationService
├── AgentOrchestrationService
├── ConversationOrchestrationFactory (工厂模式)
└── strategy/
    ├── ConversationStrategy (策略模式)
    ├── ChatConversationStrategy
    └── AgentConversationStrategy

application/task/ (只涉及角色卡，无session)
├── TaskOrchestrationService
├── TaskOrchestrationFactory (工厂模式)
└── strategy/
    ├── TaskStrategy (策略模式)
    └── ActionTaskStrategy

application/service/ (现有应用服务)
├── ChatApplicationService → conversation包
├── AgentApplicationService → conversation包
└── ActionApplicationService → task包
```

## 职责分离
- **conversation包**：处理有状态的对话（chat、agent），涉及session、记忆、工具调用
- **task包**：处理无状态的任务（action），只涉及角色卡，无session和记忆
