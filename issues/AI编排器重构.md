# AI编排器重构任务

## 背景
ChatConversationService需要重构为可插拔的编排模块，支持角色卡、记忆、工具、世界书的可选配置。

## 架构方案
采用"Builder+策略+接口可插拔"混合架构

## 执行计划

### 阶段1：核心接口设计
- [x] OrchestrationComponent - 编排组件基础接口
- [x] CharacterProvider - 角色卡提供者接口
- [x] MemoryProvider - 记忆提供者接口
- [x] ToolProvider - 工具提供者接口
- [x] WorldBookProvider - 世界书提供者接口

### 阶段2：策略接口
- [x] OrchestrationStrategy - 编排策略接口
- [x] ConversationStrategy - 对话策略实现
- [x] SummarizationStrategy - 总结策略实现

### 阶段3：Builder模式核心类
- [x] AiOrchestrator - 新的核心编排器
- [x] AiOrchestrator.Builder - 流式构建器

### 阶段4：具体实现类
- [x] DefaultCharacterProvider - 默认角色卡提供者
- [x] SpringAiMemoryProvider - Spring AI记忆提供者
- [x] FunctionToolProvider - 函数工具提供者
- [x] SummarizerCharacterProvider - 总结专用角色卡提供者

### 阶段5：工厂和配置
- [x] OrchestrationStrategyFactory - 策略工厂
- [x] OrchestrationConfig - 编排配置类

### 阶段6：重构现有代码
- [x] 重构ChatController
- [x] 重构ActionController
- [x] 添加总结博文接口

## 目标效果
```java
// 对话场景
AiOrchestrator orchestrator = AiOrchestrator.builder()
    .withCharacter("bartender")
    .withMemory("session-123")
    .withTools("blog-tools")
    .strategy(ConversationStrategy.class)
    .build();

// 总结场景
AiOrchestrator orchestrator = AiOrchestrator.builder()
    .withCharacter("summarizer")
    .strategy(SummarizationStrategy.class)
    .build();
```
