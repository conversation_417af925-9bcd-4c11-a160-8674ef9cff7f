# Advisor架构重构 - 方案3（组合模式）

## 背景
原有的DomainAdvisor + SpringAiAdvisorAdapter架构存在问题：
1. 为了使用Spring AI的RAG功能，需要直接使用QuestionAnswerAdvisor
2. 这会导致架构不统一：有些Advisor走适配器，有些直接使用Spring AI
3. 适配器模式增加了复杂度和性能损耗

## 解决方案
采用方案3（组合模式）：
- **领域层**：使用纯配置对象（AdvisorConfiguration），完全技术无关
- **基础设施层**：直接组装Spring AI的Advisor，性能最优
- **删除适配器**：不再需要DomainAdvisor和SpringAiAdvisorAdapter

## 重构完成内容

### ✅ 第一阶段：创建配置对象和领域服务
1. **AdvisorConfiguration** - 纯数据配置对象，技术无关
2. **RagSettings** - RAG功能配置
3. **MemorySettings** - 记忆功能配置
4. **AdvisorConfigurationService** - 领域配置服务

### ✅ 第二阶段：创建基础设施层组装器
1. **SimplePromptAdvisor** - 直接实现Spring AI接口的提示词Advisor
2. **SpringAiAdvisorAssembler** - 将配置转换为Spring AI Advisor的组装器

### ✅ 第三阶段：重构接口和管理器
1. **AdvisorAwareChatClientManager** - 修改为使用配置而非DomainAdvisor
2. **ConfigurationBasedChatClientManager** - 新的基于配置的实现
3. **ChatClientManager** - 修改为使用Spring AI Advisor

### ✅ 第四阶段：重构策略类
1. **AgentConversationStrategy** - 使用configurationService.buildConfiguration
2. **ChatConversationStrategy** - 使用configurationService.buildConfiguration
3. 删除了buildAdvisors方法，改用配置驱动

### ✅ 第五阶段：清理旧代码
删除的文件：
- `DomainAdvisor.java`
- `AbstractDomainAdvisor.java`
- `SpringAiAdvisorAdapter.java`
- `AdvisorConvertUtils.java`
- `SpringAiPromptAdvisorFactory.java`
- `SpringAiCharacterAdvisorFactory.java`
- `DefaultAdvisorExecutor.java`
- `SessionContextAdvisor.java`
- `DefaultAdvisorAwareChatClientManager.java`
- 所有具体的DomainAdvisor实现类

## 架构优势

### 1. 领域层完全技术无关
```java
// 领域层只有配置对象和业务逻辑
AdvisorConfiguration config = configurationService.buildConfiguration(request);
```

### 2. 性能最优
```java
// 直接使用Spring AI，无适配损耗
QuestionAnswerAdvisor ragAdvisor = QuestionAnswerAdvisor.builder(vectorStore).build();
```

### 3. 易于理解
```java
// 配置对象比抽象接口更直观
RagSettings ragSettings = RagSettings.builder()
    .similarityThreshold(0.8)
    .topK(5)
    .build();
```

### 4. 易于扩展
```java
// 新增功能只需修改配置和组装器
if (config.isRagFullyConfigured()) {
    advisors.add(createRagAdvisor(config.getRagSettings(), config.getCharacterId()));
}
```

## 新的调用流程

### 之前的流程
```
ConversationStrategy 
  -> buildAdvisors() 
  -> List<DomainAdvisor> 
  -> SpringAiAdvisorAdapter 
  -> Spring AI Advisor 
  -> ChatClient
```

### 现在的流程
```
ConversationStrategy 
  -> AdvisorConfigurationService.buildConfiguration() 
  -> AdvisorConfiguration 
  -> SpringAiAdvisorAssembler.assembleAdvisors() 
  -> List<Spring AI Advisor> 
  -> ChatClient
```

## 功能边界
- ✅ 保持现有功能完全兼容
- ✅ 为RAG功能预留接口
- ✅ 不涉及新功能开发
- ✅ 架构统一，性能优化

## 下一步
1. 实现世界书（知识库）功能
2. 完善RAG Advisor的创建逻辑
3. 添加更多配置选项（如自定义提示词模板）
