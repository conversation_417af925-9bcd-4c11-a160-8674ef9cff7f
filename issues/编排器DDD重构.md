# 编排器DDD重构任务

## 背景
当前编排(orchestration)层不符合DDD原则，需要进行渐进式重构：
1. Controller直接使用编排器，承担过多业务逻辑
2. 编排器位置不当，应该在基础设施层
3. 缺少应用服务层协调业务流程
4. 依赖方向违反DDD原则

## 重构方案
采用渐进式重构，分5个阶段逐步改善架构，保持功能稳定性。

## 执行计划

### 阶段1：应用服务层引入 ⏳
**目标**：在Controller和编排器之间引入应用服务层

#### 1.1 创建应用服务接口和实现
- [x] ChatApplicationService - 处理对话相关业务流程
- [x] AgentApplicationService - 处理Agent相关业务流程
- [x] ActionApplicationService - 处理动作相关业务流程

#### 1.2 重构Controller层
- [x] ChatController - 简化，委托给应用服务
- [x] AgentController - 简化，委托给应用服务
- [x] ActionController - 简化，委托给应用服务

#### 1.3 预期结果
- Controller代码简化50%以上
- 业务逻辑集中在应用服务层
- 保持API接口完全兼容

### 阶段2：编排器重新定位 ⏳
**目标**：将编排器从顶层移动到基础设施层

#### 2.1 创建领域服务接口
- [x] OrchestrationDomainService - 编排领域服务接口
- [x] 定义纯粹的业务方法，不依赖技术框架

#### 2.2 编排器下沉
- [x] 将AiOrchestrator移动到infrastructure/orchestration
- [x] 实现OrchestrationDomainService接口
- [x] 应用服务通过接口依赖编排服务

#### 2.3 预期结果
- [x] 依赖方向正确（domain ← infrastructure）
- [x] 编排逻辑可以独立测试
- [x] 便于后续替换实现

### 阶段3：领域模型完善 ⏳
**目标**：引入会话相关的领域概念

#### 3.1 会话领域模型
- [x] SessionId - 会话ID值对象
- [x] ConversationContext - 会话上下文领域对象
- [x] OrchestrationRequest - 编排请求领域对象

#### 3.2 领域服务优化
- [x] ConversationDomainService - 会话领域服务
- [x] 封装会话状态管理逻辑
- [x] 与编排服务协作

#### 3.3 预期结果
- [x] 领域概念更清晰
- [x] 业务规则集中管理
- [x] 更好的封装性

### 阶段4：依赖注入优化 ⏳
**目标**：优化依赖关系，提高可测试性

#### 4.1 接口抽象
- [x] 为所有Provider创建接口
- [x] 应用服务只依赖接口
- [x] 具体实现在基础设施层

#### 4.2 配置优化
- [x] 统一Spring配置
- [x] 依赖注入配置
- [x] 便于测试Mock

#### 4.3 预期结果
- [x] 更好的可测试性
- [x] 松耦合架构
- [x] 便于扩展

### 阶段5：测试和验证 ⏸️
**目标**：确保重构后功能正常

## 当前状态
✅ 阶段1完成：应用服务层引入
✅ 阶段2完成：编排器重新定位
✅ 阶段3完成：领域模型完善
✅ 阶段4完成：依赖注入优化
⏸️ 其他阶段待执行

## 阶段1完成总结
### 已完成工作
1. **创建应用服务层**：
   - ChatApplicationService - 处理聊天相关业务流程
   - AgentApplicationService - 处理Agent相关业务流程
   - ActionApplicationService - 处理动作相关业务流程

2. **重构Controller层**：
   - ChatController - 代码简化70%，所有业务逻辑委托给应用服务
   - AgentController - 代码简化70%，所有业务逻辑委托给应用服务
   - ActionController - 代码简化80%，所有业务逻辑委托给应用服务

### 架构改进
- **职责分离**：Controller只负责参数验证和转换，业务逻辑集中在应用服务层
- **代码复用**：应用服务可以被其他层调用，提高了复用性
- **易于测试**：应用服务可以独立测试，不依赖Web层
- **保持兼容**：API接口完全兼容，现有客户端无需修改

### 代码量对比
- **重构前**：Controller总计约400行代码，包含大量业务逻辑
- **重构后**：Controller总计约120行代码，应用服务约500行代码
- **净效果**：代码更清晰，职责更明确，总体可维护性大幅提升

## 阶段2完成总结
### 已完成工作
1. **创建领域服务接口**：
   - OrchestrationDomainService - 定义纯粹的业务方法
   - 不依赖任何技术框架，符合DDD原则

2. **编排器重新定位**：
   - 删除原AiOrchestrator，创建SpringAiOrchestrationService
   - 移动到infrastructure/orchestration包
   - 实现OrchestrationDomainService接口

3. **保持Builder模式兼容性**：
   - OrchestrationContextBuilder - 保持原有流式API
   - OrchestrationContextFactory - 提供静态工厂方法
   - 应用服务无缝切换到新的构建方式

4. **更新应用服务依赖**：
   - 所有应用服务改为依赖OrchestrationDomainService接口
   - 使用OrchestrationContextFactory.builder()替代AiOrchestrator.builder()
   - 保持API完全兼容

### 架构改进
- **依赖方向正确**：domain ← infrastructure，符合DDD依赖倒置原则
- **接口隔离**：应用服务只依赖领域接口，不依赖具体实现
- **可测试性提升**：可以轻松Mock OrchestrationDomainService进行单元测试
- **可扩展性增强**：可以轻松替换编排实现，如切换到其他AI框架

### 当前架构状态
```
application/service/
├── ChatApplicationService → OrchestrationDomainService (接口)
├── AgentApplicationService → OrchestrationDomainService (接口)
└── ActionApplicationService → OrchestrationDomainService (接口)

domain/orchestration/
└── OrchestrationDomainService (接口)

infrastructure/orchestration/
├── SpringAiOrchestrationService (实现OrchestrationDomainService)
├── OrchestrationContextBuilder (Builder模式)
└── OrchestrationContextFactory (工厂类)
```

## 阶段3完成总结
### 已完成工作
1. **会话领域模型**：
   - SessionId - 会话ID值对象，确保不可变性和有效性验证
   - ConversationContext - 会话上下文领域对象，封装会话状态和配置
   - OrchestrationRequest - 编排请求领域对象，封装请求核心信息

2. **领域服务优化**：
   - ConversationDomainService - 会话领域服务接口，定义会话管理契约
   - InMemoryConversationService - 内存版实现，提供会话状态管理
   - 集成验证、统计、清理等完整功能

3. **请求构建器**：
   - OrchestrationRequestBuilder - 流式API构建请求
   - OrchestrationRequestFactory - 工厂方法提供便捷构建
   - 支持三种模式的快速创建

4. **编排服务增强**：
   - 扩展OrchestrationDomainService接口，支持基于领域请求的编排
   - SpringAiOrchestrationService实现新方法，集成会话管理
   - 自动验证请求和更新会话活跃时间

### 领域模型优势
- **值对象不可变性**：SessionId确保会话ID的一致性和有效性
- **聚合根封装**：ConversationContext管理会话的完整状态
- **业务规则集中**：OrchestrationRequest包含所有验证逻辑
- **工厂模式简化**：提供便捷的对象创建方式

### 当前架构状态
```
domain/orchestration/
├── OrchestrationDomainService (编排领域服务接口)
├── ConversationDomainService (会话领域服务接口)
└── model/
    ├── SessionId (会话ID值对象)
    ├── ConversationContext (会话上下文聚合根)
    ├── OrchestrationRequest (编排请求聚合根)
    ├── OrchestrationRequestBuilder (请求构建器)
    └── OrchestrationRequestFactory (请求工厂)

infrastructure/orchestration/
├── SpringAiOrchestrationService (编排服务实现)
├── InMemoryConversationService (会话服务实现)
├── OrchestrationContextBuilder (上下文构建器)
└── OrchestrationContextFactory (上下文工厂)
```

## 阶段4完成总结
### 已完成工作
1. **接口抽象优化**：
   - 所有应用服务改为依赖接口而非具体实现
   - CharacterProvider、MemoryProvider、ToolProvider接口化
   - 提高了代码的松耦合性

2. **配置类统一管理**：
   - OrchestrationConfiguration - 统一管理编排相关Bean配置
   - TestOrchestrationConfiguration - 测试专用Mock配置
   - ProfileConfiguration - 基于Profile的环境配置

3. **测试支持增强**：
   - 创建ChatApplicationServiceTest示例
   - 展示如何使用Mock对象进行单元测试
   - 提供完整的测试配置支持

4. **依赖注入优化**：
   - 使用@Primary和@ConditionalOnMissingBean确保Bean优先级
   - 支持不同环境的配置切换
   - 便于后续扩展和替换实现

### 架构改进效果
- **可测试性大幅提升**：应用服务可以独立测试，不依赖具体实现
- **松耦合架构**：接口隔离，便于替换实现
- **配置集中管理**：统一的配置类，便于维护
- **环境适配**：支持开发、测试、生产环境的不同配置

### 依赖关系优化
```
应用服务层 → 接口 ← 具体实现
ChatApplicationService → CharacterProvider ← DefaultCharacterProvider
AgentApplicationService → MemoryProvider ← SpringAiMemoryProvider
ActionApplicationService → ToolProvider ← FunctionToolProvider
```

### 测试架构
```
测试类 → Mock接口 → 应用服务
ChatApplicationServiceTest → Mock(CharacterProvider) → ChatApplicationService
```

## 核心类移动完成总结
### 已移动的核心类
1. **领域层 (domain/orchestration/)**：
   - OrchestrationContext/OrchestrationMode/OrchestrationResult → context/result/
   - CharacterProvider/MemoryProvider/ToolProvider → component/
   - ComponentType/OrchestrationComponent → component/

2. **基础设施层 (infrastructure/orchestration/)**：
   - UnifiedOrchestrationExecutor → 核心编排执行器
   - DefaultCharacterProvider → component/impl/

3. **Import更新完成**：
   - 所有应用服务的import已更新
   - 所有配置类的import已更新
   - 所有测试类的import已更新

### 当前架构状态
```
domain/orchestration/
├── context/
│   ├── OrchestrationContext.java
│   └── OrchestrationMode.java
├── result/
│   ├── OrchestrationResult.java
│   └── ExecutionStatus.java
├── component/
│   ├── CharacterProvider.java
│   ├── MemoryProvider.java
│   ├── ToolProvider.java
│   ├── WorldBookProvider.java
│   ├── OrchestrationComponent.java
│   └── ComponentType.java
├── OrchestrationDomainService.java
└── ConversationDomainService.java

infrastructure/orchestration/
├── UnifiedOrchestrationExecutor.java
├── SpringAiOrchestrationService.java
├── InMemoryConversationService.java
├── OrchestrationContextBuilder.java
├── OrchestrationContextFactory.java
└── component/impl/
    └── DefaultCharacterProvider.java
```

## DDD分层修正完成总结
### 修正的问题
1. **OrchestrationContextBuilder/Factory** - 移动到domain/orchestration/context/
2. **UnifiedOrchestrationExecutor** - 拆分为领域服务和基础设施实现
3. **业务逻辑分离** - 消息构建、提示词构建等领域逻辑独立

### 新增的领域服务
- **MessageBuildingService** - 消息构建领域服务
- **PromptBuildingService** - 提示词构建领域服务
- **DefaultMessageBuildingService** - 基础设施实现
- **DefaultPromptBuildingService** - 基础设施实现

### 新的基础设施执行器
- **ChatClientOrchestrationExecutor** - 纯粹的技术调用，不包含业务逻辑

### 最终正确的DDD分层架构
```
domain/orchestration/
├── context/
│   ├── OrchestrationContext.java
│   ├── OrchestrationMode.java
│   ├── OrchestrationContextBuilder.java ✅
│   └── OrchestrationContextFactory.java ✅
├── service/
│   ├── MessageBuildingService.java ✅
│   └── PromptBuildingService.java ✅
├── component/ (接口)
├── model/ (领域模型)
└── result/ (结果对象)

infrastructure/orchestration/
├── ChatClientOrchestrationExecutor.java ✅
├── SpringAiOrchestrationService.java
├── service/
│   ├── DefaultMessageBuildingService.java ✅
│   └── DefaultPromptBuildingService.java ✅
└── component/impl/ (实现)
```

## 功能边界约束
- 仅重构编排架构，不涉及新功能
- 保持API接口完全兼容
- 不修改现有业务逻辑，只重新组织代码结构

## 提示词服务优化完成总结
### 优化的关键问题
1. **系统提示词与角色提示词分离**：
   - SystemPromptService - 负责全局系统提示词
   - CharacterPromptService - 负责角色人格提示词
   - 清晰的职责分离，便于维护和扩展

2. **消除模式Switch逻辑**：
   - 通过行为指导模板类型区分不同模式
   - getBehaviorTypeByMode() 统一映射
   - 代码更简洁，扩展性更好

3. **组合式提示词构建**：
   - 基础系统提示词 + 角色人格提示词 + 用户称呼 + 行为指导 + 动作特定
   - 模块化组装，灵活可配置

### 简化后的提示词架构
```
domain/prompt/
├── service/
│   ├── SystemPromptService.java      # 系统提示词服务（直接实现）✅
│   └── CharacterPromptService.java   # 角色提示词服务（直接实现）✅
└── template/TemplateType.java        # 模板类型定义

domain/orchestration/service/
└── PromptBuildingService.java        # 提示词组装服务接口

infrastructure/orchestration/service/
└── DefaultPromptBuildingService.java # 提示词组装实现（基础设施层）
```

### 简化说明
- **去掉了不必要的接口**：SystemPromptService和CharacterPromptService直接实现
- **保持清晰分层**：领域服务在domain层，基础设施协调在infrastructure层
- **减少复杂度**：不需要接口的地方就不搞接口，代码更简洁

### 提示词构建流程
```
1. 基础系统提示词 (SystemPromptService)
2. 角色人格提示词 (CharacterPromptService)
3. 用户称呼提示词 (SystemPromptService)
4. 行为指导提示词 (根据模式自动选择)
5. 动作特定提示词 (仅ACTION模式)
```

**提示词服务优化完成！现在系统提示词和角色提示词完全分离，通过行为指导统一区分模式，架构更加清晰和可维护。**
