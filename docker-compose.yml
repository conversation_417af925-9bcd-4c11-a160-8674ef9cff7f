services:
  blog-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: blog-server
    networks:
      - 1panel-network
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SPRING_SERVLET_MULTIPART_MAX_FILE_SIZE=10MB
      - SPRING_SERVLET_MULTIPART_MAX_REQUEST_SIZE=10MB
    restart: always
    ports:
      - "8080:8080"
networks:
  1panel-network:
    external: true
