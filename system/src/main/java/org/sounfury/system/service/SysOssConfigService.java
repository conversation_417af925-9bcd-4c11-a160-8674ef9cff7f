package org.sounfury.system.service;



import org.sounfury.jooq.page.PageRepDto;
import org.sounfury.jooq.page.PageReqDto;
import org.sounfury.system.dto.rep.SysOssConfigRep;
import org.sounfury.system.dto.req.SysOssConfigReq;
import java.util.Collection;
import java.util.List;

/**
 * 对象存储配置Service接口
 *
 * <AUTHOR> Li
 * <AUTHOR>
 * @date 2021-08-13
 */
public interface SysOssConfigService {

    /**
     * 初始化OSS配置
     */
    void init();

    /**
     * 查询单个
     */
    SysOssConfigRep queryById(Long ossConfigId);

    /**
     * 查询列表
     */
    List<SysOssConfigRep> queryList();

    /**
     * 根据新增业务对象插入对象存储配置
     *
     * @param bo 对象存储配置新增业务对象
     * @return 结果
     */
    Boolean insert(SysOssConfigReq bo);

    /**
     * 根据编辑业务对象修改对象存储配置
     *
     * @param bo 对象存储配置编辑业务对象
     * @return 结果
     */
    Boolean update(SysOssConfigReq bo);

    /**
     * 校验并删除数据
     *
     * @param ids     主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids);

    /**
     * 启用停用状态
     */
    void updateOssConfigStatus(SysOssConfigReq bo);

}
